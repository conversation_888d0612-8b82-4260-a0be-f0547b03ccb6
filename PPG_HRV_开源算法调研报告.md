# PPG信号计算HRV的开源算法调研报告

## 概述

本报告调研了通过PPG信号计算心率变异性(HRV)的开源算法实现，重点分析了NeuroKit2库中的相关算法，为Android项目提供技术参考。

## 主要开源库

### 1. NeuroKit2 (Python) - 最推荐 ⭐⭐⭐⭐⭐

**GitHub**: https://github.com/neuropsychology/NeuroKit  
**语言**: Python  
**许可证**: MIT License  

#### 核心功能
- 完整的PPG信号处理管道
- 多种峰值检测算法
- 全面的HRV参数计算
- 信号质量评估
- 可视化功能

#### PPG峰值检测算法

##### 1. Elgendi算法 (默认推荐)
```python
def _ppg_findpeaks_elgendi(signal, sampling_rate=1000, 
                          peakwindow=0.111, beatwindow=0.667, 
                          beatoffset=0.02, mindelay=0.3):
    # 核心步骤：
    # 1. 信号预处理：去除负值，平方处理
    # 2. 双移动平均滤波
    # 3. 阈值检测
    # 4. 峰值定位
```

**特点**:
- 适用于紧急救援人员等恶劣环境
- 鲁棒性强，抗噪声能力好
- 参数可调节

##### 2. Bishop算法 (MSPTD)
```python
def _ppg_findpeaks_bishop(signal):
    # 多尺度峰值和谷值检测
    # 1. 信号去趋势
    # 2. 构建局部最大值/最小值标度图
    # 3. 找到最优尺度
    # 4. 提取峰值和起始点
```

**特点**:
- 适用于短信号(6秒@100Hz)
- 同时检测峰值和起始点
- 基于多尺度分析

##### 3. Charlton算法 (MSPTDfast)
```python
def _ppg_findpeaks_charlton(signal, sampling_rate=1000, win_durn=6):
    # MSPTDfastv2实现
    # 1. 分窗处理
    # 2. 可选下采样
    # 3. 多尺度检测
    # 4. 峰值校正
```

**特点**:
- 高效实现
- 支持长信号处理
- 可配置窗口大小

#### HRV计算实现

##### 时域指标
```python
def hrv_time(peaks, sampling_rate=1000):
    # 核心时域指标计算
    out["MeanNN"] = np.nanmean(rri)           # 平均RR间期
    out["SDNN"] = np.nanstd(rri, ddof=1)      # RR间期标准差
    out["RMSSD"] = np.sqrt(np.nanmean(diff_rri**2))  # 相邻RR差值均方根
    out["pNN50"] = nn50 / (len(diff_rri) + 1) * 100  # >50ms差值百分比
    # ... 更多指标
```

##### 频域指标
- LF功率 (0.04-0.15Hz)
- HF功率 (0.15-0.4Hz)  
- LF/HF比值
- 总功率

##### 非线性指标
- SD1, SD2 (Poincaré图)
- 近似熵、样本熵
- 去趋势波动分析(DFA)

#### 使用示例
```python
import neurokit2 as nk

# 处理PPG信号
ppg = nk.ppg_simulate(duration=60, sampling_rate=100, heart_rate=70)
signals, info = nk.ppg_process(ppg, sampling_rate=100)

# 计算HRV
hrv_results = nk.ppg_intervalrelated(signals, sampling_rate=100)
```

### 2. pyHRV (Python)

**GitHub**: https://github.com/PGomes92/pyhrv  
**特点**:
- 专门的HRV分析工具包
- 支持78个HRV参数
- 支持ECG、PPG、BVP等信号
- 可视化和报告生成

### 3. HeartPy (Python)

**GitHub**: https://github.com/paulvangentcom/heartrate_analysis_python  
**特点**:
- 适用于智能手表数据
- PPG峰值检测
- 基础HRV分析

### 4. Android/Java参考实现

**ECGMonitor项目**: https://github.com/jingjie-li/ECGMonitor  
**特点**:
- 包含Android APP实现
- 支持PPG信号处理和HRV计算
- 可作为Android移植参考

## 核心算法分析

### PPG信号预处理流程
```
原始PPG信号 → 带通滤波(0.5-8Hz) → 基线去除 → 平滑处理 → 峰值检测
```

### Elgendi峰值检测详细步骤
1. **信号预处理**: 去除负值，信号平方
2. **双移动平均**: 
   - 峰值窗口MA (默认0.111s)
   - 节拍窗口MA (默认0.667s)
3. **阈值计算**: `threshold = MA_beat + offset * mean(signal²)`
4. **波形识别**: 找到超过阈值的波形段
5. **峰值定位**: 在每个波形段内找最显著峰值
6. **最小间隔**: 强制峰值间最小间隔(默认0.3s)

### HRV参数计算公式

#### 时域参数
- **SDNN**: `std(RR_intervals)`
- **RMSSD**: `sqrt(mean(diff(RR_intervals)²))`
- **pNN50**: `count(|diff(RR)| > 50ms) / total_diffs * 100`

#### 频域参数
- **LF**: 0.04-0.15Hz功率
- **HF**: 0.15-0.4Hz功率  
- **LF/HF**: 交感/副交感比值

## Android移植建议

### 方案1: 直接移植NeuroKit2算法
1. **优点**: 算法成熟，文档完整
2. **实现步骤**:
   - 移植Elgendi峰值检测算法
   - 实现基础HRV时域计算
   - 添加信号质量评估

### 方案2: 简化实现
```java
public class PPGHRVCalculator {
    // 1. PPG峰值检测
    public List<Integer> detectPeaks(double[] ppgSignal, int samplingRate) {
        // 实现Elgendi算法核心逻辑
    }
    
    // 2. HRV计算
    public HRVMetrics calculateHRV(List<Integer> peaks, int samplingRate) {
        // 计算SDNN, RMSSD, pNN50等指标
    }
}
```

### 关键技术要点
1. **采样率要求**: 建议64Hz以上
2. **数据长度**: 至少2-5分钟进行可靠HRV分析
3. **信号质量**: 需要质量评估和伪影去除
4. **实时性**: 可采用滑动窗口实现实时计算

## 性能指标参考
- **覆盖率**: >83%
- **敏感性**: >87%  
- **正预测值**: >98%
- **时间精度**: <20ms RMSSD差异

## 推荐实现路径

1. **第一阶段**: 实现Elgendi峰值检测算法
2. **第二阶段**: 添加基础时域HRV计算(SDNN, RMSSD, pNN50)
3. **第三阶段**: 集成信号质量评估
4. **第四阶段**: 优化实时性能

## 总结

NeuroKit2提供了最完整和成熟的PPG HRV算法实现，建议以其为参考进行Android移植。Elgendi算法在鲁棒性和准确性方面表现优秀，适合作为核心峰值检测算法。
