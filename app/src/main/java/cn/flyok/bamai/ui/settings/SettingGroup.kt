package cn.flyok.bamai.ui.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.common.ClickEffects.lightScale
import cn.flyok.bamai.ui.common.ClickEffects.strongScale
import cn.flyok.bamai.ui.theme.*

/**
 * 设置项分组组件 - 完全复刻iOS端的分组TableView
 * 
 * 特点：
 * - 白色背景卡片
 * - 分组圆角处理（顶部、中间、底部）
 * - 分隔线处理
 * - 点击高亮效果
 */
@Composable
fun SettingGroup(
    items: List<SettingItem>,
    onItemClick: (SettingItem) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(BamaiDimensions.cardCornerRadius),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFB)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column {
            items.forEachIndexed { index, item ->
                val cornerPosition = when {
                    items.size == 1 -> CornerPosition.ALL
                    index == 0 -> CornerPosition.TOP
                    index == items.size - 1 -> CornerPosition.BOTTOM
                    else -> CornerPosition.MIDDLE
                }
                
                SettingItem(
                    item = item,
                    cornerPosition = cornerPosition,
                    showSeparator = index < items.size - 1,
                    onClick = { onItemClick(item) }
                )
            }
        }
    }
}

/**
 * 单个设置项组件 - 完全复刻iOS端SettingTableViewCell
 */
@Composable
private fun SettingItem(
    item: SettingItem,
    cornerPosition: CornerPosition,
    showSeparator: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 外层容器负责动效
    val clickModifier = if (item.actionType == SettingActionType.IDS) {
        modifier.strongScale(onClick = onClick)
    } else {
        modifier.lightScale(onClick = onClick)
    }

    Box(
        modifier = clickModifier.fillMaxWidth()
    ) {
        // 列表项主体 - 包含背景和所有视觉元素
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color.Transparent,
                    shape = when (cornerPosition) {
                        CornerPosition.TOP -> RoundedCornerShape(
                            topStart = BamaiDimensions.cardCornerRadius,
                            topEnd = BamaiDimensions.cardCornerRadius
                        )
                        CornerPosition.BOTTOM -> RoundedCornerShape(
                            bottomStart = BamaiDimensions.cardCornerRadius,
                            bottomEnd = BamaiDimensions.cardCornerRadius
                        )
                        CornerPosition.ALL -> RoundedCornerShape(BamaiDimensions.cardCornerRadius)
                        else -> RoundedCornerShape(0.dp)
                    }
                )
            .padding(horizontal = 20.dp, vertical = 20.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Image(
                painter = painterResource(id = item.icon),
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(15.dp))
            
            // 标题和副标题
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = item.title,
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 14.sp
                    ),
                    color = BamaiPrimaryText
                )
                
                item.subtitle?.let { subtitle ->
                    Spacer(modifier = Modifier.height(5.dp))
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontSize = 13.sp
                        ),
                        color = Color(0xFFA1968A)
                    )
                }
            }
            
            // 右侧文本
            item.rightText?.let { rightText ->
                Text(
                    text = if (item.actionType == SettingActionType.ABOUT_APP) {
                        "1.0.0" // 固定显示版本号
                    } else {
                        rightText
                    },
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 14.sp
                    ),
                    color = BamaiButtonColor,
                    modifier = Modifier.padding(end = if (item.showArrow) 10.dp else 0.dp)
                )
            }
            
            // 箭头或复制图标
            if (item.showArrow) {
                Image(
                    painter = painterResource(
                        id = if (item.actionType == SettingActionType.IDS) {
                            R.drawable.set_copy
                        } else {
                            R.drawable.home_arrow
                        }
                    ),
                    contentDescription = null,
                    modifier = Modifier.size(
                        if (item.actionType == SettingActionType.IDS) 10.dp else 18.dp
                    )
                )
            }
        }
        
        // 分隔线
        if (showSeparator) {
            HorizontalDivider(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = 59.dp), // 图标宽度 + 间距
                color = Color.Transparent,
                thickness = 0.5.dp
            )
        }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SettingGroupPreview() {
    BamaiTheme {
        val mockItems = listOf(
//            SettingItem(
//                icon = R.drawable.set_qus,
//                title = "问题反馈",
//                actionType = SettingActionType.FEEDBACK
//            ),
//            SettingItem(
//                icon = R.drawable.set_redbook,
//                title = "联系我们",
//                actionType = SettingActionType.CONTACT
//            ),
            SettingItem(
                icon = R.drawable.set_app,
                title = "关于APP",
                rightText = "1.0.0",
                showArrow = false,
                actionType = SettingActionType.ABOUT_APP
            )
        )
        
        SettingGroup(
            items = mockItems,
            onItemClick = {},
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun SettingGroupSingleItemPreview() {
    BamaiTheme {
        val mockItems = listOf(
            SettingItem(
                icon = R.drawable.set_id,
                title = "ID",
                rightText = "BM123456789",
                actionType = SettingActionType.IDS
            )
        )
        
        SettingGroup(
            items = mockItems,
            onItemClick = {},
            modifier = Modifier.padding(20.dp)
        )
    }
}
