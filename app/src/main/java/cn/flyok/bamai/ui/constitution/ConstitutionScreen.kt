package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.data.UserManager
import cn.flyok.bamai.ui.theme.*

/**
 * 体质页面 - 完全复刻iOS端ConController
 *
 * 包含：
 * - 导航标题和装饰背景
 * - 雷达图头部视图
 * - 体质信息卡片
 * - 今日养生卡片
 * - 饮食养生卡片
 * - 调养方案卡片
 * - 易感疾病卡片
 * - 关于我的体质卡片
 * - 底部医疗信息来源链接
 */
@Composable
fun ConstitutionScreen(
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val userManager = remember { UserManager.getInstance(context) }

    // 获取会员状态
    val vipStatus by remember { derivedStateOf { userManager.isVip } }

    // 模拟测量数据状态（实际应该从数据库或网络获取）
    val hasEnoughData = true // TODO: 从实际数据源获取

    // 创建体质数据，根据会员状态决定显示内容
    val constitutionData = remember(vipStatus, hasEnoughData) {
        ConstitutionData.mock().copy(
            isVip = vipStatus,
            hasEnoughData = hasEnoughData
        )
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(BamaiViewBackColor)
    ) {
        // 导航栏区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp)
        ) {
            // 导航标题
            Text(
                text = "体质分析",
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 24.sp
                ),
                color = Color(0xFF333333),
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = 20.dp, bottom = 6.dp)
            )

            // 右上角装饰背景
            Image(
                painter = painterResource(id = R.drawable.home_zzbg),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = 15.dp)
                    .size(width = 131.dp, height = 153.dp),
                contentScale = ContentScale.Fit
            )
        }

        // 体质内容列表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 20.dp)
        ) {
            // 雷达图头部
            item {
                ConstitutionRadarHeader(
                    constitutionData = constitutionData,
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 16.dp)
                )
            }

            // 体质信息卡片
            item {
                ConstitutionInfoCard(
                    constitutionData = constitutionData,
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 6.dp)
                )
            }

            // 今日养生卡片
            item {
                TodayWellnessCard(
                    constitutionData = constitutionData,
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 6.dp)
                )
            }

            // 饮食养生卡片
            item {
                DietWellnessCard(
                    constitutionData = constitutionData,
                    onCardClick = { /* TODO: 跳转菜谱页面 */ },
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 6.dp)
                )
            }

            // 调养方案卡片
            item {
                NursingPlanCard(
                    constitutionData = constitutionData,
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 6.dp)
                )
            }

            // 易感疾病卡片
            item {
                DiseaseRiskCard(
                    constitutionData = constitutionData,
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 6.dp)
                )
            }

            // 关于我的体质卡片
            item {
                AboutConstitutionCard(
                    constitutionData = constitutionData,
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 6.dp)
                )
            }

            // 底部医疗信息来源
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(40.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "医疗信息来源",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontSize = 12.sp
                        ),
                        color = Color(0xFFA1968A),
                        modifier = Modifier.clickable(
                            onClick = { /* TODO: 跳转医疗信息来源页面 */ },
                            indication = null,
                            interactionSource = remember { MutableInteractionSource() }
                        )
                    )
                }
            }
        }
    }
}
