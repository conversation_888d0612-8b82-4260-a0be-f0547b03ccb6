package cn.flyok.bamai.ui.privacy

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*

/**
 * 隐私政策同意页面 - 完全复刻iOS端AppPrivacyController
 * 
 * 特点：
 * - 全屏背景图片
 * - 双层立即开启按钮
 * - 隐私政策和用户协议链接
 * - 加载状态和错误提示
 */
@Composable
fun PrivacyAgreementScreen(
    isLoading: Boolean = false,
    errorMessage: String? = null,
    onAgreeClick: () -> Unit,
    onPrivacyPolicyClick: () -> Unit,
    onUserAgreementClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(BamaiViewBackColor)
    ) {
        // 背景图片
        Image(
            painter = painterResource(id = R.drawable.appprivacy_bg),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        
        // 底部内容区域
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .padding(horizontal = 47.5.dp, vertical = 125.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 协议说明文字
            PrivacyAgreementText(
                onPrivacyPolicyClick = onPrivacyPolicyClick,
                onUserAgreementClick = onUserAgreementClick,
                modifier = Modifier.padding(bottom = 5.dp)
            )
            
            // 立即开启按钮
            PrivacyAgreeButton(
                isLoading = isLoading,
                onAgreeClick = onAgreeClick,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // 错误提示
            if (errorMessage != null) {
                Text(
                    text = errorMessage,
                    fontSize = 12.sp,
                    color = Color(0xFFFF4444),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 20.dp)
                )
            }
        }
    }
}

/**
 * 隐私协议文字组件
 */
@Composable
private fun PrivacyAgreementText(
    onPrivacyPolicyClick: () -> Unit,
    onUserAgreementClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val agreementText = buildAnnotatedString {
        append("点击按钮表示您同意我们的")
        
        // 隐私政策链接
        pushStringAnnotation(tag = "privacy", annotation = "privacy_policy")
        withStyle(
            style = SpanStyle(
                color = Color(0xFF7E5F31),
                fontWeight = FontWeight.Medium
            )
        ) {
            append("《隐私政策》")
        }
        pop()
        
        append("及")
        
        // 用户协议链接
        pushStringAnnotation(tag = "agreement", annotation = "user_agreement")
        withStyle(
            style = SpanStyle(
                color = Color(0xFF7E5F31),
                fontWeight = FontWeight.Medium
            )
        ) {
            append("《用户协议》")
        }
        pop()
    }
    
    Text(
        text = agreementText,
        fontSize = 10.sp,
        color = Color(0xFF333333),
        textAlign = TextAlign.Center,
        lineHeight = 14.sp,
        modifier = modifier.fillMaxWidth()
    )
}

/**
 * 立即开启按钮组件 - 双层设计
 */
@Composable
private fun PrivacyAgreeButton(
    isLoading: Boolean,
    onAgreeClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(280.dp, 48.dp)
            .clip(RoundedCornerShape(22.dp))
            .background(Color(0xFFAE8772))
            .clickable(
                enabled = !isLoading,
                onClick = onAgreeClick,
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ),
        contentAlignment = Alignment.Center
    ) {
        // 内层按钮
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(5.dp)
                .clip(RoundedCornerShape(17.dp))
                .background(Color(0xFFAE8772))
                .border(
                    width = 1.dp,
                    color = Color(0xFFE5D6CE),
                    shape = RoundedCornerShape(17.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            if (isLoading) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                    Text(
                        text = "正在初始化...",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            } else {
                Text(
                    text = "立即开启",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PrivacyAgreementScreenPreview() {
    BamaiNoRippleTheme {
        PrivacyAgreementScreen(
            isLoading = false,
            errorMessage = null,
            onAgreeClick = {},
            onPrivacyPolicyClick = {},
            onUserAgreementClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PrivacyAgreementScreenLoadingPreview() {
    BamaiNoRippleTheme {
        PrivacyAgreementScreen(
            isLoading = true,
            errorMessage = null,
            onAgreeClick = {},
            onPrivacyPolicyClick = {},
            onUserAgreementClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PrivacyAgreementScreenErrorPreview() {
    BamaiNoRippleTheme {
        PrivacyAgreementScreen(
            isLoading = false,
            errorMessage = "网络连接失败，请检查网络设置",
            onAgreeClick = {},
            onPrivacyPolicyClick = {},
            onUserAgreementClick = {}
        )
    }
}
