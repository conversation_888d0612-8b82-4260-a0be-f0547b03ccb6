package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*

/**
 * PPG信号质量评估器
 * 实现信噪比计算、信号稳定性评估和自动质量控制
 * 确保心率测量的可靠性和准确性
 */
class SignalQualityAssessment {
    
    companion object {
        private const val TAG = "SignalQualityAssessment"
        
        // 质量评估参数 - 降低阈值以便收集更多数据
        private const val QUALITY_WINDOW_SIZE = 60 // 2秒窗口（假设30fps）
        private const val MIN_SNR_THRESHOLD = -10.0 // 最小信噪比阈值（降低）
        private const val MAX_NOISE_RATIO = 0.8 // 最大噪声比例（提高）
        
        // 稳定性评估参数
        private const val STABILITY_WINDOW_SIZE = 90 // 3秒窗口
        private const val MAX_VARIANCE_THRESHOLD = 0.1 // 最大方差阈值
        
        // 运动检测参数
        private const val MOTION_DETECTION_WINDOW = 30 // 1秒窗口
        private const val MOTION_THRESHOLD = 0.2 // 运动检测阈值
    }
    
    // 信号质量历史
    private val qualityHistory = mutableListOf<QualityMetrics>()
    
    // 信号缓冲区用于质量分析
    private val signalBuffer = mutableListOf<Double>()
    
    // 当前质量状态
    private var currentQuality = SignalQuality.UNKNOWN
    
    /**
     * 信号质量等级枚举
     */
    enum class SignalQuality {
        EXCELLENT,  // 优秀 - SNR > 10, 稳定性好
        GOOD,       // 良好 - SNR > 6, 稳定性较好
        FAIR,       // 一般 - SNR > 3, 稳定性中等
        POOR,       // 差 - SNR < 3 或不稳定
        UNKNOWN     // 未知 - 数据不足
    }
    
    /**
     * 评估信号质量
     * 
     * @param signalValue 经过滤波的信号值
     * @param rawValue 原始信号值（用于噪声分析）
     * @return 当前信号质量等级
     */
    fun assessQuality(signalValue: Double, rawValue: Double): SignalQuality {
        // 添加到信号缓冲区
        addToSignalBuffer(signalValue)
        
        // 检查是否有足够的数据进行评估
        if (signalBuffer.size < QUALITY_WINDOW_SIZE) {
            return SignalQuality.UNKNOWN
        }
        
        // 计算质量指标
        val metrics = calculateQualityMetrics(signalValue, rawValue)
        
        // 添加到质量历史
        qualityHistory.add(metrics)
        if (qualityHistory.size > 10) {
            qualityHistory.removeAt(0)
        }
        
        // 评估整体质量
        currentQuality = evaluateOverallQuality(metrics)
        
        // 只在质量发生变化时输出日志，避免刷屏
        if (currentQuality != qualityHistory.lastOrNull()?.let { evaluateOverallQuality(it) }) {
            Log.d(TAG, "Signal quality changed to: $currentQuality, SNR=${metrics.snr}, " +
                    "stability=${metrics.stability}, motion=${metrics.motionLevel}")
        }
        
        return currentQuality
    }
    
    /**
     * 获取当前信号质量
     */
    fun getCurrentQuality(): SignalQuality {
        return currentQuality
    }
    
    /**
     * 检查信号是否适合测量
     */
    fun isSignalSuitableForMeasurement(): Boolean {
        return currentQuality in listOf(SignalQuality.EXCELLENT, SignalQuality.GOOD, SignalQuality.FAIR)
    }


    
    /**
     * 获取质量改善建议
     */
    fun getQualityImprovementSuggestions(): List<String> {
        val suggestions = mutableListOf<String>()
        
        if (qualityHistory.isEmpty()) {
            return suggestions
        }
        
        val latestMetrics = qualityHistory.last()
        
        when {
            latestMetrics.snr < MIN_SNR_THRESHOLD -> {
                suggestions.add("请确保手指完全覆盖相机镜头")
                suggestions.add("请保持手指稳定，避免移动")
            }
            latestMetrics.stability < 0.5 -> {
                suggestions.add("请保持手指稳定，减少晃动")
                suggestions.add("请确保手指与镜头接触良好")
            }
            latestMetrics.motionLevel > MOTION_THRESHOLD -> {
                suggestions.add("检测到运动，请保持静止")
                suggestions.add("请将手机放在稳定的表面上")
            }
            latestMetrics.brightness < 0.3 || latestMetrics.brightness > 0.9 -> {
                suggestions.add("请调整光照条件")
                suggestions.add("确保闪光灯正常工作")
            }
        }
        
        if (suggestions.isEmpty()) {
            suggestions.add("信号质量良好，继续保持")
        }
        
        return suggestions
    }
    
    /**
     * 重置质量评估器
     */
    fun reset() {
        signalBuffer.clear()
        qualityHistory.clear()
        currentQuality = SignalQuality.UNKNOWN
        Log.d(TAG, "Signal quality assessment reset")
    }
    
    /**
     * 添加信号到缓冲区
     */
    private fun addToSignalBuffer(value: Double) {
        signalBuffer.add(value)
        
        // 保持缓冲区大小
        if (signalBuffer.size > STABILITY_WINDOW_SIZE) {
            signalBuffer.removeAt(0)
        }
    }
    
    /**
     * 计算质量指标
     */
    private fun calculateQualityMetrics(signalValue: Double, rawValue: Double): QualityMetrics {
        val windowData = signalBuffer.takeLast(QUALITY_WINDOW_SIZE)
        
        // 1. 计算信噪比 (SNR)
        val snr = calculateSNR(windowData)
        
        // 2. 计算信号稳定性
        val stability = calculateStability(windowData)
        
        // 3. 检测运动水平
        val motionLevel = detectMotionLevel(windowData)
        
        // 4. 评估亮度质量
        val brightness = evaluateBrightness(rawValue)
        
        // 5. 计算信号强度
        val signalStrength = calculateSignalStrength(windowData)
        
        return QualityMetrics(
            snr = snr,
            stability = stability,
            motionLevel = motionLevel,
            brightness = brightness,
            signalStrength = signalStrength,
            timestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * 计算信噪比
     */
    private fun calculateSNR(data: List<Double>): Double {
        if (data.size < 10) return 0.0
        
        val mean = data.average()
        val variance = data.map { (it - mean).pow(2) }.average()
        val stdDev = sqrt(variance)
        
        // 信号功率 vs 噪声功率的比值
        val signalPower = mean.pow(2)
        val noisePower = variance
        
        return if (noisePower > 0) {
            10 * log10(signalPower / noisePower)
        } else {
            Double.MAX_VALUE
        }
    }
    
    /**
     * 计算信号稳定性
     */
    private fun calculateStability(data: List<Double>): Double {
        if (data.size < STABILITY_WINDOW_SIZE) return 0.0
        
        // 计算相邻样本的变化率
        val changes = mutableListOf<Double>()
        for (i in 1 until data.size) {
            changes.add(abs(data[i] - data[i-1]))
        }
        
        val meanChange = changes.average()
        val maxChange = changes.maxOrNull() ?: 0.0
        
        // 稳定性 = 1 - (平均变化 / 最大可能变化)
        return if (maxChange > 0) {
            1.0 - (meanChange / maxChange)
        } else {
            1.0
        }
    }
    
    /**
     * 检测运动水平
     */
    private fun detectMotionLevel(data: List<Double>): Double {
        if (data.size < MOTION_DETECTION_WINDOW) return 0.0
        
        val recentData = data.takeLast(MOTION_DETECTION_WINDOW)
        
        // 计算高频变化（可能的运动伪影）
        var highFreqChanges = 0
        for (i in 2 until recentData.size) {
            val change1 = abs(recentData[i] - recentData[i-1])
            val change2 = abs(recentData[i-1] - recentData[i-2])
            
            // 检测快速变化
            if (change1 > 0.1 && change2 > 0.1) {
                highFreqChanges++
            }
        }
        
        return highFreqChanges.toDouble() / recentData.size
    }
    
    /**
     * 评估亮度质量
     */
    private fun evaluateBrightness(rawValue: Double): Double {
        // 将原始值归一化到0-1范围
        val normalizedBrightness = rawValue / 255.0
        
        // 理想亮度范围是0.3-0.8
        return when {
            normalizedBrightness < 0.2 -> 0.2 // 太暗
            normalizedBrightness > 0.9 -> 0.2 // 太亮
            normalizedBrightness in 0.3..0.8 -> 1.0 // 理想
            else -> 0.6 // 可接受
        }
    }
    
    /**
     * 计算信号强度
     */
    private fun calculateSignalStrength(data: List<Double>): Double {
        if (data.isEmpty()) return 0.0
        
        val range = (data.maxOrNull() ?: 0.0) - (data.minOrNull() ?: 0.0)
        val mean = abs(data.average())
        
        // 信号强度基于幅度和平均值
        return (range + mean) / 2.0
    }
    
    /**
     * 评估整体质量
     */
    private fun evaluateOverallQuality(metrics: QualityMetrics): SignalQuality {
        var score = 0.0
        
        // SNR权重 40% - 降低阈值
        score += when {
            metrics.snr > 0 -> 4.0
            metrics.snr > -10 -> 3.0
            metrics.snr > -20 -> 2.0
            else -> 1.0
        } * 0.4
        
        // 稳定性权重 30%
        score += when {
            metrics.stability > 0.8 -> 4.0
            metrics.stability > 0.6 -> 3.0
            metrics.stability > 0.4 -> 2.0
            else -> 1.0
        } * 0.3
        
        // 运动水平权重 20% (越低越好)
        score += when {
            metrics.motionLevel < 0.1 -> 4.0
            metrics.motionLevel < 0.2 -> 3.0
            metrics.motionLevel < 0.3 -> 2.0
            else -> 1.0
        } * 0.2
        
        // 亮度权重 10%
        score += when {
            metrics.brightness > 0.8 -> 4.0
            metrics.brightness > 0.6 -> 3.0
            metrics.brightness > 0.4 -> 2.0
            else -> 1.0
        } * 0.1
        
        return when {
            score >= 3.0 -> SignalQuality.EXCELLENT
            score >= 2.2 -> SignalQuality.GOOD
            score >= 1.5 -> SignalQuality.FAIR
            else -> SignalQuality.POOR
        }
    }
    
    /**
     * 获取详细的质量报告
     */
    fun getDetailedQualityReport(): QualityReport {
        val recentMetrics = qualityHistory.takeLast(5)
        
        return QualityReport(
            currentQuality = currentQuality,
            averageSNR = if (recentMetrics.isNotEmpty()) {
                recentMetrics.map { it.snr }.average()
            } else 0.0,
            averageStability = if (recentMetrics.isNotEmpty()) {
                recentMetrics.map { it.stability }.average()
            } else 0.0,
            averageMotionLevel = if (recentMetrics.isNotEmpty()) {
                recentMetrics.map { it.motionLevel }.average()
            } else 0.0,
            suggestions = getQualityImprovementSuggestions()
        )
    }
    
    /**
     * 质量指标数据类
     */
    private data class QualityMetrics(
        val snr: Double,
        val stability: Double,
        val motionLevel: Double,
        val brightness: Double,
        val signalStrength: Double,
        val timestamp: Long
    )
    
    /**
     * 质量报告数据类
     */
    data class QualityReport(
        val currentQuality: SignalQuality,
        val averageSNR: Double,
        val averageStability: Double,
        val averageMotionLevel: Double,
        val suggestions: List<String>
    )
}
