package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*

/**
 * RR间隔处理器 - 基于NeuroKit intervals_process实现
 * 
 * 负责从峰值提取RR间隔，并进行预处理：
 * - 异常值检测和修正
 * - 插值处理
 * - 去趋势处理
 */
class IntervalProcessor {
    
    companion object {
        private const val TAG = "IntervalProcessor"
        
        // 处理参数
        private const val MIN_RR_INTERVAL = 300.0  // 最小RR间隔 (ms) - 对应200 BPM
        private const val MAX_RR_INTERVAL = 2000.0 // 最大RR间隔 (ms) - 对应30 BPM
        private const val ARTIFACT_THRESHOLD = 0.2 // 异常值检测阈值（20%变化）
    }
    
    /**
     * RR间隔数据类
     */
    data class RRIntervals(
        val intervals: List<Double>,    // RR间隔（毫秒）
        val timestamps: List<Long>,     // 对应时间戳
        val quality: List<Double>,      // 每个间隔的质量评分
        val samplingRate: Double = 30.0 // 原始采样率
    )
    
    /**
     * 处理后的间隔数据
     */
    data class ProcessedIntervals(
        val intervals: List<Double>,        // 处理后的RR间隔
        val timestamps: List<Double>,       // 对应时间戳（秒）
        val interpolatedRate: Int,          // 插值采样率
        val artifactCount: Int,             // 异常值数量
        val qualityScore: Double            // 整体质量评分
    )
    
    /**
     * 从峰值提取RR间隔
     */
    fun extractRRIntervals(peaks: List<POSPeakDetector.SignalMeasurement>): RRIntervals {
        if (peaks.size < 2) {
            return RRIntervals(emptyList(), emptyList(), emptyList())
        }
        
        val intervals = mutableListOf<Double>()
        val timestamps = mutableListOf<Long>()
        val qualities = mutableListOf<Double>()
        
        // 计算连续峰值间的时间间隔
        for (i in 1 until peaks.size) {
            val interval = (peaks[i].timestamp - peaks[i-1].timestamp).toDouble() // 毫秒
            
            // 基本质量检查
            val quality = assessIntervalQuality(interval, intervals.lastOrNull())
            
            intervals.add(interval)
            timestamps.add(peaks[i].timestamp)
            qualities.add(quality)
        }
        
        Log.d(TAG, "Extracted ${intervals.size} RR intervals")
        return RRIntervals(intervals, timestamps, qualities)
    }
    
    /**
     * 处理RR间隔
     */
    fun processIntervals(
        rrIntervals: RRIntervals,
        correctArtifacts: Boolean = true,
        interpolate: Boolean = true,
        interpolationRate: Int = 4,
        detrend: Boolean = true
    ): ProcessedIntervals {
        
        var intervals = rrIntervals.intervals.toMutableList()
        var timestamps = rrIntervals.timestamps.map { it / 1000.0 }.toMutableList() // 转换为秒
        var artifactCount = 0
        
        // 1. 异常值检测和修正
        if (correctArtifacts) {
            val correctionResult = correctArtifacts(intervals)
            intervals = correctionResult.first.toMutableList()
            artifactCount = correctionResult.second
        }
        
        // 2. 插值处理
        var processedIntervals = intervals.toList()
        var processedTimestamps = timestamps.toList()
        var finalRate = interpolationRate
        
        if (interpolate && intervals.size >= 3) {
            val interpolated = interpolateIntervals(intervals, timestamps, interpolationRate)
            processedIntervals = interpolated.intervals
            processedTimestamps = interpolated.timestamps
            finalRate = interpolated.samplingRate
        }
        
        // 3. 去趋势处理
        if (detrend && processedIntervals.size >= 10) {
            processedIntervals = detrendSignal(processedIntervals)
        }
        
        // 4. 计算整体质量评分
        val qualityScore = calculateOverallQuality(rrIntervals.quality, artifactCount, intervals.size)
        
        Log.d(TAG, "Processed intervals: ${processedIntervals.size}, artifacts: $artifactCount, quality: $qualityScore")
        
        return ProcessedIntervals(
            intervals = processedIntervals,
            timestamps = processedTimestamps,
            interpolatedRate = finalRate,
            artifactCount = artifactCount,
            qualityScore = qualityScore
        )
    }
    
    /**
     * 评估单个间隔的质量
     */
    private fun assessIntervalQuality(interval: Double, previousInterval: Double?): Double {
        var quality = 1.0
        
        // 检查是否在合理范围内
        if (interval < MIN_RR_INTERVAL || interval > MAX_RR_INTERVAL) {
            quality *= 0.3
        }
        
        // 检查与前一个间隔的变化
        previousInterval?.let { prev ->
            val change = abs(interval - prev) / prev
            if (change > ARTIFACT_THRESHOLD) {
                quality *= 0.5
            }
        }
        
        return quality
    }
    
    /**
     * 异常值检测和修正
     */
    private fun correctArtifacts(intervals: List<Double>): Pair<List<Double>, Int> {
        if (intervals.size < 3) return Pair(intervals, 0)
        
        val corrected = intervals.toMutableList()
        var artifactCount = 0
        
        // 使用移动中位数方法检测异常值
        val windowSize = minOf(5, intervals.size)
        
        for (i in 1 until intervals.size - 1) {
            val start = maxOf(0, i - windowSize / 2)
            val end = minOf(intervals.size, i + windowSize / 2 + 1)
            val window = intervals.subList(start, end)
            val median = window.sorted()[window.size / 2]
            
            val deviation = abs(intervals[i] - median) / median
            
            // 如果偏差超过阈值，使用插值修正
            if (deviation > ARTIFACT_THRESHOLD) {
                val interpolated = (intervals[i-1] + intervals[i+1]) / 2.0
                corrected[i] = interpolated
                artifactCount++
                Log.d(TAG, "Corrected artifact at index $i: ${intervals[i]} -> $interpolated")
            }
        }
        
        return Pair(corrected, artifactCount)
    }
    
    /**
     * 插值处理数据类
     */
    data class InterpolatedData(
        val intervals: List<Double>,
        val timestamps: List<Double>,
        val samplingRate: Int
    )
    
    /**
     * 间隔插值处理
     */
    private fun interpolateIntervals(
        intervals: List<Double>,
        timestamps: List<Double>,
        targetRate: Int
    ): InterpolatedData {
        if (intervals.size < 2) {
            return InterpolatedData(intervals, timestamps, targetRate)
        }
        
        // 创建均匀时间网格
        val startTime = timestamps.first()
        val endTime = timestamps.last()
        val duration = endTime - startTime
        val numPoints = (duration * targetRate).toInt()
        
        if (numPoints < 2) {
            return InterpolatedData(intervals, timestamps, targetRate)
        }
        
        val uniformTimes = (0 until numPoints).map { 
            startTime + it * duration / (numPoints - 1)
        }
        
        // 线性插值
        val interpolatedIntervals = uniformTimes.map { t ->
            interpolateValue(t, timestamps, intervals)
        }
        
        return InterpolatedData(interpolatedIntervals, uniformTimes, targetRate)
    }
    
    /**
     * 线性插值单个值
     */
    private fun interpolateValue(t: Double, times: List<Double>, values: List<Double>): Double {
        if (t <= times.first()) return values.first()
        if (t >= times.last()) return values.last()
        
        // 找到插值区间
        for (i in 0 until times.size - 1) {
            if (t >= times[i] && t <= times[i + 1]) {
                val ratio = (t - times[i]) / (times[i + 1] - times[i])
                return values[i] + ratio * (values[i + 1] - values[i])
            }
        }
        
        return values.last()
    }
    
    /**
     * 去趋势处理（简单多项式去趋势）
     */
    private fun detrendSignal(intervals: List<Double>): List<Double> {
        if (intervals.size < 10) return intervals
        
        // 计算线性趋势
        val n = intervals.size
        val x = (0 until n).map { it.toDouble() }
        val y = intervals
        
        // 最小二乘法拟合直线
        val sumX = x.sum()
        val sumY = y.sum()
        val sumXY = x.zip(y) { xi, yi -> xi * yi }.sum()
        val sumX2 = x.map { it * it }.sum()
        
        val slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
        val intercept = (sumY - slope * sumX) / n
        
        // 去除趋势
        return intervals.mapIndexed { i, value ->
            value - (slope * i + intercept - intercept) // 保持均值不变
        }
    }
    
    /**
     * 计算整体质量评分
     */
    private fun calculateOverallQuality(
        individualQualities: List<Double>,
        artifactCount: Int,
        totalCount: Int
    ): Double {
        if (individualQualities.isEmpty()) return 0.0
        
        val avgQuality = individualQualities.average()
        val artifactRatio = artifactCount.toDouble() / totalCount
        val artifactPenalty = 1.0 - artifactRatio * 0.5 // 异常值比例惩罚
        
        return (avgQuality * artifactPenalty).coerceIn(0.0, 1.0)
    }
}
