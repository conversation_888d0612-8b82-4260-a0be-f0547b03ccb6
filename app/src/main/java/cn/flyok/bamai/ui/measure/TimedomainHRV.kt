package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*

/**
 * 时域HRV指标计算器 - 基于NeuroKit hrv_time实现
 * 
 * 计算所有主要的时域心率变异性指标：
 * - 基础统计指标 (MeanNN, SDNN, etc.)
 * - 差值相关指标 (RMSSD, pNN50, etc.)
 * - 几何指标 (TINN, HTI)
 */
class TimedomainHRV {
    
    companion object {
        private const val TAG = "TimedomainHRV"
    }
    
    /**
     * 时域HRV指标数据类
     */
    data class TimedomainMetrics(
        // 基础统计指标
        val meanNN: Double,         // 平均RR间隔 (ms)
        val sdNN: Double,           // RR间隔标准差 (ms)
        val medianNN: Double,       // RR间隔中位数 (ms)
        val madNN: Double,          // 中位数绝对偏差 (ms)
        val cvNN: Double,           // 变异系数 (%)
        val iqrNN: Double,          // 四分位距 (ms)
        val minNN: Double,          // 最小RR间隔 (ms)
        val maxNN: Double,          // 最大RR间隔 (ms)
        val rangeNN: Double,        // RR间隔范围 (ms)

        // 差值相关指标
        val rmssd: Double,          // 连续差值均方根 (ms)
        val sdsd: Double,           // 连续差值标准差 (ms)
        val pnn50: Double,          // >50ms差值百分比 (%)
        val pnn20: Double,          // >20ms差值百分比 (%)
        val cvsd: Double,           // RMSSD变异系数 (%)

        // 几何指标
        val tinn: Double,           // 三角插值指数 (ms)
        val hti: Double,            // HRV三角指数

        // 其他指标
        val prc20NN: Double,        // 20th百分位数 (ms)
        val prc80NN: Double,        // 80th百分位数 (ms)
        val sdrmssd: Double         // SDNN/RMSSD比值
    ) : java.io.Serializable
    
    /**
     * 计算时域HRV指标
     */
    fun calculate(intervals: List<Double>): TimedomainMetrics {
        if (intervals.isEmpty()) {
            return createEmptyMetrics()
        }
        
        Log.d(TAG, "Calculating time-domain HRV for ${intervals.size} intervals")
        
        // 基础统计指标
        val meanNN = intervals.average()
        val sdNN = calculateStandardDeviation(intervals)
        val medianNN = calculateMedian(intervals)
        val madNN = calculateMAD(intervals, medianNN)
        val cvNN = if (meanNN > 0) (sdNN / meanNN) * 100 else 0.0
        val iqrNN = calculateIQR(intervals)
        val minNN = intervals.minOrNull() ?: 0.0
        val maxNN = intervals.maxOrNull() ?: 0.0
        val rangeNN = maxNN - minNN
        
        // 差值相关指标
        val differences = calculateSuccessiveDifferences(intervals)
        val rmssd = calculateRMSSD(differences)
        val sdsd = calculateStandardDeviation(differences)
        val pnn50 = calculatePNNx(differences, 50.0)
        val pnn20 = calculatePNNx(differences, 20.0)
        val cvsd = if (meanNN > 0) (rmssd / meanNN) * 100 else 0.0
        
        // 几何指标
        val tinn = calculateTINN(intervals)
        val hti = calculateHTI(intervals)
        
        // 百分位数
        val sortedIntervals = intervals.sorted()
        val prc20NN = calculatePercentile(sortedIntervals, 20.0)
        val prc80NN = calculatePercentile(sortedIntervals, 80.0)
        
        // 比值指标
        val sdrmssd = if (rmssd > 0) sdNN / rmssd else 0.0
        
        return TimedomainMetrics(
            meanNN = meanNN,
            sdNN = sdNN,
            medianNN = medianNN,
            madNN = madNN,
            cvNN = cvNN,
            iqrNN = iqrNN,
            minNN = minNN,
            maxNN = maxNN,
            rangeNN = rangeNN,
            rmssd = rmssd,
            sdsd = sdsd,
            pnn50 = pnn50,
            pnn20 = pnn20,
            cvsd = cvsd,
            tinn = tinn,
            hti = hti,
            prc20NN = prc20NN,
            prc80NN = prc80NN,
            sdrmssd = sdrmssd
        )
    }
    
    /**
     * 计算标准差
     */
    private fun calculateStandardDeviation(values: List<Double>): Double {
        if (values.size < 2) return 0.0
        
        val mean = values.average()
        val variance = values.map { (it - mean).pow(2) }.average()
        return sqrt(variance)
    }
    
    /**
     * 计算中位数
     */
    private fun calculateMedian(values: List<Double>): Double {
        if (values.isEmpty()) return 0.0
        
        val sorted = values.sorted()
        val size = sorted.size
        
        return if (size % 2 == 0) {
            (sorted[size / 2 - 1] + sorted[size / 2]) / 2.0
        } else {
            sorted[size / 2]
        }
    }
    
    /**
     * 计算中位数绝对偏差 (MAD)
     */
    private fun calculateMAD(values: List<Double>, median: Double): Double {
        if (values.isEmpty()) return 0.0
        
        val deviations = values.map { abs(it - median) }
        return calculateMedian(deviations)
    }
    
    /**
     * 计算四分位距 (IQR)
     */
    private fun calculateIQR(values: List<Double>): Double {
        if (values.isEmpty()) return 0.0
        
        val sorted = values.sorted()
        val q1 = calculatePercentile(sorted, 25.0)
        val q3 = calculatePercentile(sorted, 75.0)
        return q3 - q1
    }
    
    /**
     * 计算百分位数
     */
    private fun calculatePercentile(sortedValues: List<Double>, percentile: Double): Double {
        if (sortedValues.isEmpty()) return 0.0
        
        val index = (percentile / 100.0) * (sortedValues.size - 1)
        val lowerIndex = floor(index).toInt()
        val upperIndex = ceil(index).toInt()
        
        if (lowerIndex == upperIndex) {
            return sortedValues[lowerIndex]
        }
        
        val weight = index - lowerIndex
        return sortedValues[lowerIndex] * (1 - weight) + sortedValues[upperIndex] * weight
    }
    
    /**
     * 计算连续差值
     */
    private fun calculateSuccessiveDifferences(intervals: List<Double>): List<Double> {
        if (intervals.size < 2) return emptyList()
        
        return intervals.zipWithNext { a, b -> abs(b - a) }
    }
    
    /**
     * 计算RMSSD (连续差值均方根)
     */
    private fun calculateRMSSD(differences: List<Double>): Double {
        if (differences.isEmpty()) return 0.0
        
        val squaredDiffs = differences.map { it.pow(2) }
        return sqrt(squaredDiffs.average())
    }
    
    /**
     * 计算pNNx (超过x毫秒的差值百分比)
     */
    private fun calculatePNNx(differences: List<Double>, threshold: Double): Double {
        if (differences.isEmpty()) return 0.0
        
        val count = differences.count { it > threshold }
        return (count.toDouble() / differences.size) * 100.0
    }
    
    /**
     * 计算TINN (三角插值指数)
     * 简化实现：使用直方图方法
     */
    private fun calculateTINN(intervals: List<Double>): Double {
        if (intervals.size < 20) return 0.0 // 需要足够的数据
        
        // 创建直方图
        val minVal = intervals.minOrNull() ?: 0.0
        val maxVal = intervals.maxOrNull() ?: 0.0
        val range = maxVal - minVal
        
        if (range <= 0) return 0.0
        
        val binWidth = 7.8125 // 标准bin宽度 (ms)
        val numBins = (range / binWidth).toInt() + 1
        val histogram = IntArray(numBins)
        
        // 填充直方图
        for (interval in intervals) {
            val binIndex = ((interval - minVal) / binWidth).toInt().coerceIn(0, numBins - 1)
            histogram[binIndex]++
        }
        
        // 找到峰值
        val maxCount = histogram.maxOrNull() ?: 0
        if (maxCount == 0) return 0.0
        
        // 简化的TINN计算：基于直方图的基线宽度
        val nonZeroBins = histogram.count { it > 0 }
        return nonZeroBins * binWidth
    }
    
    /**
     * 计算HTI (HRV三角指数)
     */
    private fun calculateHTI(intervals: List<Double>): Double {
        if (intervals.size < 20) return 0.0
        
        // 创建直方图
        val minVal = intervals.minOrNull() ?: 0.0
        val maxVal = intervals.maxOrNull() ?: 0.0
        val range = maxVal - minVal
        
        if (range <= 0) return 0.0
        
        val binWidth = 7.8125 // 标准bin宽度 (ms)
        val numBins = (range / binWidth).toInt() + 1
        val histogram = IntArray(numBins)
        
        // 填充直方图
        for (interval in intervals) {
            val binIndex = ((interval - minVal) / binWidth).toInt().coerceIn(0, numBins - 1)
            histogram[binIndex]++
        }
        
        // HTI = 总数 / 峰值高度
        val maxCount = histogram.maxOrNull() ?: 0
        return if (maxCount > 0) intervals.size.toDouble() / maxCount else 0.0
    }
    
    /**
     * 创建空的指标对象
     */
    private fun createEmptyMetrics(): TimedomainMetrics {
        return TimedomainMetrics(
            meanNN = 0.0, sdNN = 0.0, medianNN = 0.0, madNN = 0.0, cvNN = 0.0,
            iqrNN = 0.0, minNN = 0.0, maxNN = 0.0, rangeNN = 0.0,
            rmssd = 0.0, sdsd = 0.0, pnn50 = 0.0, pnn20 = 0.0, cvsd = 0.0,
            tinn = 0.0, hti = 0.0, prc20NN = 0.0, prc80NN = 0.0, sdrmssd = 0.0
        )
    }
}
