package cn.flyok.bamai.ui.home.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*

/**
 * 首页头部组件 - 完全复刻iOS端HomeHeaderView
 * 
 * 布局结构：
 * - 背景图片 (home_yhbg)
 * - Logo区域 (根据脉象类型显示不同logo)
 * - 结果标题 (30pt粗体)
 * - 时间标签 (12pt灰色)
 * - 一键测量按钮 (Android端专用，去掉iWatch按钮)
 */
@Composable
fun HomeHeader(
    pulseResult: String = "暂无数据",
    timeText: String = "--",
    logoRes: Int = R.drawable.home_logo_none,
    onMeasureClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidth = configuration.screenWidthDp.dp
    
    // 计算iOS端的头部高度比例 (277/375 * 屏幕宽度)
    val headerHeight = with(density) {
        (277f / 375f * screenWidth.toPx()).toDp()
    }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(), // 改为自适应高度
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 背景图片区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(headerHeight),
            contentAlignment = Alignment.Center
        ) {
            // 背景图片
            Image(
                painter = painterResource(id = R.drawable.home_yhbg),
                contentDescription = "首页背景",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
            
            // Logo区域
            Image(
                painter = painterResource(id = logoRes),
                contentDescription = "脉象Logo",
                modifier = Modifier
                    .size(
                        width = BamaiDimensions.logoWidth,
                        height = BamaiDimensions.logoHeight
                    )
                    .offset(y = BamaiDimensions.statusBarOffset)
            )
        }
        
        // 结果标题
        Text(
            text = pulseResult,
            style = MaterialTheme.typography.headlineLarge,
            color = BamaiPrimaryText,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = BamaiDimensions.paddingXLarge)
        )
        
        // 时间标签
        Text(
            text = timeText,
            style = MaterialTheme.typography.bodyMedium,
            color = BamaiSecondaryText,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = BamaiDimensions.paddingMedium)
        )
        
        // 一键测量按钮布局
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = BamaiDimensions.paddingMedium,
                    bottom = BamaiDimensions.paddingLarge
                ),
            contentAlignment = Alignment.Center
        ) {
            // 一键测量按钮
            Button(
                onClick = onMeasureClick,
                modifier = Modifier.size(width = 130.dp, height = 46.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFAE8772)
                ),
                shape = RoundedCornerShape(23.dp)
            ) {
                Text(
                    text = "一键测量",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                )
            }
        }
    }
}

/**
 * 把脉App专用按钮组件 - 复刻iOS端的按钮样式
 * 
 * 特点：
 * - 双层设计：外层圆角23，内层圆角18
 * - 棕色背景 (#AE8772)
 * - 边框颜色 (#C2B19A)
 */
@Composable
private fun BamaiButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(
                width = BamaiDimensions.buttonWidth,
                height = BamaiDimensions.buttonHeight
            )
            .clip(RoundedCornerShape(BamaiDimensions.buttonCornerRadius))
            .background(BamaiButtonColor),
        contentAlignment = Alignment.Center
    ) {
        Button(
            onClick = onClick,
            modifier = Modifier
                .fillMaxSize()
                .padding(BamaiDimensions.paddingSmall),
            colors = ButtonDefaults.buttonColors(
                containerColor = BamaiButtonColor,
                contentColor = Color.White
            ),
            shape = RoundedCornerShape(BamaiDimensions.innerButtonCornerRadius),
            border = androidx.compose.foundation.BorderStroke(
                width = BamaiDimensions.borderWidth,
                color = BamaiButtonBorder
            ),
            contentPadding = PaddingValues(0.dp)
        ) {
            Text(
                text = text,
                style = MaterialTheme.typography.titleSmall,
                color = Color.White
            )
        }
    }
}

/**
 * 根据脉象类型获取对应的Logo资源ID
 * 复刻iOS端的getHomeLogo函数逻辑
 */
fun getHomeLogoResource(pulseType: PulseType): Int {
    return when (pulseType) {
        PulseType.PING_MAI -> R.drawable.home_logo_pingmai
        PulseType.HONG_SHU_MAI -> R.drawable.home_logo_hongshumai
        PulseType.HUA_SHU_MAI -> R.drawable.home_logo_huashumai
        PulseType.RUO_CHI_MAI -> R.drawable.home_logo_ruochimai
        PulseType.SE_SHU_MAI -> R.drawable.home_logo_seshumai
        PulseType.XI_RUO_MAI -> R.drawable.home_logo_xiruomai
        PulseType.XI_SHU_MAI -> R.drawable.home_logo_xishumai
        PulseType.XUAN_HUA_MAI -> R.drawable.home_logo_xuanhuamai
        PulseType.XUAN_JIE_MAI -> R.drawable.home_logo_xuanjiemai
        PulseType.XUAN_SHU_MAI -> R.drawable.home_logo_xuanshumai
        PulseType.CHI_MAI -> R.drawable.home_logo_ruochimai  // 使用弱迟脉的图标
        PulseType.SHU_MAI -> R.drawable.home_logo_hongshumai // 使用洪数脉的图标
        PulseType.NONE -> R.drawable.home_logo_none
    }
}

/**
 * 脉象类型枚举 - 对应iOS端的脉象分类
 */
enum class PulseType {
    PING_MAI,       // 平脉
    HONG_SHU_MAI,   // 洪数脉
    HUA_SHU_MAI,    // 滑数脉
    RUO_CHI_MAI,    // 弱迟脉
    SE_SHU_MAI,     // 涩数脉
    XI_RUO_MAI,     // 细弱脉
    XI_SHU_MAI,     // 细数脉
    XUAN_HUA_MAI,   // 弦滑脉
    XUAN_JIE_MAI,   // 弦结脉
    XUAN_SHU_MAI,   // 弦数脉
    CHI_MAI,        // 迟脉
    SHU_MAI,        // 数脉
    NONE            // 无数据
}

@Preview(showBackground = true)
@Composable
fun HomeHeaderPreview() {
    BamaiTheme {
        HomeHeader(
            pulseResult = "平脉",
            timeText = "2025/07/24 10:30",
            logoRes = R.drawable.home_logo_pingmai
        )
    }
}

@Preview(showBackground = true)
@Composable
fun HomeHeaderNoDataPreview() {
    BamaiTheme {
        HomeHeader()
    }
}
