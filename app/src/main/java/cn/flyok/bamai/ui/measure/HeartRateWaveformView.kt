package cn.flyok.bamai.ui.measure

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.*

/**
 * 心率波形显示组件
 * 实时显示PPG信号波形和心跳检测结果
 */
@Composable
fun HeartRateWaveformView(
    signalData: List<Double>,
    peakIndices: List<Int>,
    currentHeartRate: Int,
    signalQuality: SignalQualityAssessment.SignalQuality,
    isActive: Boolean,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(MaterialTheme.colorScheme.surface)
            .padding(16.dp)
    ) {
        // 标题和心率显示
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "心率波形",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 信号质量指示器
                SignalQualityIndicator(
                    quality = signalQuality,
                    modifier = Modifier.padding(end = 8.dp)
                )
                
                // 当前心率显示
                Text(
                    text = if (currentHeartRate > 0) "${currentHeartRate} BPM" else "--",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = if (isActive) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 波形显示区域
        WaveformCanvas(
            signalData = signalData,
            peakIndices = peakIndices,
            isActive = isActive,
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 波形统计信息
        WaveformStats(
            signalData = signalData,
            signalQuality = signalQuality
        )
    }
}

/**
 * 信号质量指示器
 */
@Composable
private fun SignalQualityIndicator(
    quality: SignalQualityAssessment.SignalQuality,
    modifier: Modifier = Modifier
) {
    val (color, text) = when (quality) {
        SignalQualityAssessment.SignalQuality.EXCELLENT -> Color(0xFF4CAF50) to "优秀"
        SignalQualityAssessment.SignalQuality.GOOD -> Color(0xFF8BC34A) to "良好"
        SignalQualityAssessment.SignalQuality.FAIR -> Color(0xFFFF9800) to "一般"
        SignalQualityAssessment.SignalQuality.POOR -> Color(0xFFF44336) to "较差"
        SignalQualityAssessment.SignalQuality.UNKNOWN -> Color(0xFF9E9E9E) to "未知"
    }
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(color, shape = androidx.compose.foundation.shape.CircleShape)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = color
        )
    }
}

/**
 * 波形绘制画布
 */
@Composable
private fun WaveformCanvas(
    signalData: List<Double>,
    peakIndices: List<Int>,
    isActive: Boolean,
    modifier: Modifier = Modifier
) {
    val waveformColor = if (isActive) Color(0xFF2196F3) else Color(0xFFBDBDBD)
    val peakColor = Color(0xFFFF5722)
    val gridColor = Color(0xFFE0E0E0)
    
    Canvas(modifier = modifier) {
        drawWaveformBackground(gridColor)
        
        if (signalData.isNotEmpty()) {
            drawWaveform(signalData, waveformColor)
            drawPeaks(signalData, peakIndices, peakColor)
        } else {
            drawPlaceholder()
        }
    }
}

/**
 * 绘制波形背景网格
 */
private fun DrawScope.drawWaveformBackground(gridColor: Color) {
    val width = size.width
    val height = size.height

    // 绘制水平网格线
    for (i in 0..4) {
        val y = (height * i / 4).toFloat()
        drawLine(
            color = gridColor,
            start = Offset(0f, y),
            end = Offset(width, y),
            strokeWidth = 1.dp.toPx()
        )
    }

    // 绘制垂直网格线
    for (i in 0..10) {
        val x = (width * i / 10).toFloat()
        drawLine(
            color = gridColor,
            start = Offset(x, 0f),
            end = Offset(x, height),
            strokeWidth = 1.dp.toPx()
        )
    }
}

/**
 * 绘制波形
 */
private fun DrawScope.drawWaveform(signalData: List<Double>, color: Color) {
    if (signalData.size < 2) return
    
    val width = size.width
    val height = size.height
    val dataSize = signalData.size
    
    // 计算信号的最大值和最小值用于归一化
    val minValue = signalData.minOrNull() ?: -1.0
    val maxValue = signalData.maxOrNull() ?: 1.0
    val range = maxValue - minValue
    
    if (range == 0.0) return
    
    val path = Path()
    var isFirstPoint = true
    
    for (i in signalData.indices) {
        val x = width * i / (dataSize - 1)
        val normalizedValue = (signalData[i] - minValue) / range
        val y = height * (1 - normalizedValue) // 翻转Y轴，使波形向上为正

        if (isFirstPoint) {
            path.moveTo(x.toFloat(), y.toFloat())
            isFirstPoint = false
        } else {
            path.lineTo(x.toFloat(), y.toFloat())
        }
    }
    
    drawPath(
        path = path,
        color = color,
        style = Stroke(width = 2.dp.toPx(), cap = StrokeCap.Round)
    )
}

/**
 * 绘制峰值标记
 */
private fun DrawScope.drawPeaks(signalData: List<Double>, peakIndices: List<Int>, color: Color) {
    if (signalData.isEmpty() || peakIndices.isEmpty()) return
    
    val width = size.width
    val height = size.height
    val dataSize = signalData.size
    
    val minValue = signalData.minOrNull() ?: -1.0
    val maxValue = signalData.maxOrNull() ?: 1.0
    val range = maxValue - minValue
    
    if (range == 0.0) return
    
    peakIndices.forEach { peakIndex ->
        if (peakIndex in signalData.indices) {
            val x = (width * peakIndex / (dataSize - 1)).toFloat()
            val normalizedValue = (signalData[peakIndex] - minValue) / range
            val y = (height * (1 - normalizedValue)).toFloat()

            // 绘制峰值点
            drawCircle(
                color = color,
                radius = 4.dp.toPx(),
                center = Offset(x, y)
            )

            // 绘制峰值标记线
            drawLine(
                color = color,
                start = Offset(x, y - 10.dp.toPx()),
                end = Offset(x, y + 10.dp.toPx()),
                strokeWidth = 2.dp.toPx()
            )
        }
    }
}

/**
 * 绘制占位符
 */
private fun DrawScope.drawPlaceholder() {
    val width = size.width
    val height = size.height
    val centerY = (height / 2).toFloat()

    // 绘制基线
    drawLine(
        color = Color(0xFFBDBDBD),
        start = Offset(0f, centerY),
        end = Offset(width, centerY),
        strokeWidth = 2.dp.toPx()
    )
}

/**
 * 波形统计信息
 */
@Composable
private fun WaveformStats(
    signalData: List<Double>,
    signalQuality: SignalQualityAssessment.SignalQuality
) {
    if (signalData.isEmpty()) return
    
    val mean = signalData.average()
    val variance = signalData.map { (it - mean).pow(2) }.average()
    val stdDev = sqrt(variance)
    val range = (signalData.maxOrNull() ?: 0.0) - (signalData.minOrNull() ?: 0.0)
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        StatItem(
            label = "均值",
            value = String.format("%.3f", mean)
        )
        StatItem(
            label = "标准差",
            value = String.format("%.3f", stdDev)
        )
        StatItem(
            label = "范围",
            value = String.format("%.3f", range)
        )
        StatItem(
            label = "样本数",
            value = signalData.size.toString()
        )
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
