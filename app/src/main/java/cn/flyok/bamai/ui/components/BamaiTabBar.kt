package cn.flyok.bamai.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*

/**
 * 把脉App底部TabBar - 完全复刻iOS端AppTabBarController
 * 
 * 特点：
 * - 3个Tab：首页、体质、设置
 * - 背景色：#F8F6F2
 * - 阴影效果
 * - 图标上移10dp效果
 * - 无涟漪效果
 */
@Composable
fun BamaiTabBar(
    selectedTab: Int,
    onTabSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(60.dp), // 调整TabBar高度为60dp
        color = Color(0xFFF8F6F2), // iOS端的tabBar背景色
        shadowElevation = 8.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    start = 40.dp,
                    end = 40.dp,
                    bottom = 10.dp // 减少底部安全区域
                ),
            horizontalArrangement = Arrangement.SpaceBetween, // 改为SpaceBetween让Tab更分散
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 首页Tab
            TabItem(
                iconRes = if (selectedTab == 0) R.drawable.tab_icon_jr_click else R.drawable.tab_icon_jr_default,
                isSelected = selectedTab == 0,
                onClick = { onTabSelected(0) }
            )
            
            // 体质Tab
            TabItem(
                iconRes = if (selectedTab == 1) R.drawable.tab_icon_tz_click else R.drawable.tab_icon_tz_default,
                isSelected = selectedTab == 1,
                onClick = { onTabSelected(1) }
            )
            
            // 设置Tab
            TabItem(
                iconRes = if (selectedTab == 2) R.drawable.tab_icon_sz_click else R.drawable.tab_icon_sz_default,
                isSelected = selectedTab == 2,
                onClick = { onTabSelected(2) }
            )
        }
    }
}

/**
 * TabBar单个项目组件
 */
@Composable
private fun TabItem(
    iconRes: Int,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(70.dp) // 增大点击区域
            .clickable(
                onClick = onClick,
                indication = null, // 禁用涟漪效果
                interactionSource = remember { MutableInteractionSource() }
            ),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = iconRes),
            contentDescription = null,
            modifier = Modifier
                .size(32.dp) // 增大图标尺寸从24dp到32dp
                .offset(y = 10.dp) // 向下偏移
        )
    }
}

/**
 * Tab页面枚举
 */
enum class TabPage(val index: Int) {
    HOME(0),    // 首页
    CONSTITUTION(1), // 体质
    SETTINGS(2) // 设置
}

/**
 * Tab状态管理
 */
@Composable
fun rememberTabBarState(initialTab: TabPage = TabPage.HOME): MutableState<TabPage> {
    return remember { mutableStateOf(initialTab) }
}

@Preview(showBackground = true)
@Composable
fun BamaiTabBarPreview() {
    BamaiTheme {
        var selectedTab by remember { mutableStateOf(0) }
        BamaiTabBar(
            selectedTab = selectedTab,
            onTabSelected = { selectedTab = it }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun BamaiTabBarSelectedPreview() {
    BamaiTheme {
        var selectedTab by remember { mutableStateOf(1) }
        BamaiTabBar(
            selectedTab = selectedTab,
            onTabSelected = { selectedTab = it }
        )
    }
}
