package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*

/**
 * PPG信号处理器 - 基于POS_WANG算法 (IEEE 2017)
 *
 * 实现Wang等人的POS (Plane-Orthogonal-to-Skin)算法：
 * <PERSON>, <PERSON><PERSON>, <PERSON>, A. <PERSON>, <PERSON>, S., & <PERSON>, <PERSON> (2017).
 * Algorithmic principles of remote PPG.
 * IEEE Transactions on Biomedical Engineering, 64(7), 1479-1491.
 *
 * 这是目前最权威和准确的无监督rPPG方法
 */
class PPGSignalProcessor {

    companion object {
        private const val TAG = "PPGSignalProcessor"

        // POS算法参数 - 基于IEEE 2017论文
        private const val SAMPLE_RATE = 30.0 // Android摄像头采样率
        private const val WINDOW_SIZE_SEC = 1.6 // POS算法窗口大小（秒）
        private const val LOW_CUTOFF = 0.75  // 低频截止频率 (Hz) - 对应45 BPM
        private const val HIGH_CUTOFF = 3.0  // 高频截止频率 (Hz) - 对应180 BPM

        // 信号缓冲区大小
        private const val SIGNAL_BUFFER_SIZE = 300 // 10秒 * 30fps
    }
    
    // RGB信号缓冲区 - 存储RGB三通道的平均值
    private val rgbBuffer = mutableListOf<DoubleArray>() // [R, G, B]

    // POS算法输出缓冲区 - 重叠累加结果
    private val posSignalBuffer = mutableListOf<Double>()

    // 窗口大小（样本数）
    private val windowSize = (WINDOW_SIZE_SEC * SAMPLE_RATE).toInt()

    // POS算法的重叠累加缓冲区 (H矩阵)
    private val overlapAddBuffer = mutableListOf<Double>()

    // 带通滤波器系数
    private val filterCoefficients = calculateButterworthCoefficients()

    // 滤波器状态变量
    private var x1 = 0.0
    private var x2 = 0.0
    private var y1 = 0.0
    private var y2 = 0.0
    
    /**
     * 处理新的PPG信号样本 - 基于POS_WANG算法 (IEEE 2017)
     *
     * @param pixels 当前帧的像素数组
     * @param width 图像宽度
     * @param height 图像高度
     * @return 经过POS算法处理的信号值
     */
    fun processSample(pixels: IntArray, width: Int, height: Int): Double {
        // 1. 计算RGB三通道的平均值
        val rgb = calculateRGBAverage(pixels, width, height)

        // 2. 添加到RGB缓冲区
        addToRGBBuffer(rgb)

        // 3. 应用POS算法
        val posSignal = applyPOSAlgorithm()

        // 4. 带通滤波
        val filtered = if (posSignal != null) {
            applyBandpassFilter(posSignal)
        } else {
            0.0
        }

        // 5. 额外的平滑处理来减少噪声
        val smoothed = applyAdditionalSmoothing(filtered)

        // 6. 添加到输出缓冲区
        addToPOSBuffer(smoothed)

        return smoothed
    }

    // 平滑滤波缓冲区
    private val smoothingBuffer = mutableListOf<Double>()
    private val smoothingWindowSize = 3

    /**
     * 额外的平滑处理来减少噪声
     */
    private fun applyAdditionalSmoothing(input: Double): Double {
        smoothingBuffer.add(input)

        // 保持窗口大小
        if (smoothingBuffer.size > smoothingWindowSize) {
            smoothingBuffer.removeAt(0)
        }

        // 简单移动平均
        return smoothingBuffer.average()
    }
    
    /**
     * 获取处理后的信号缓冲区
     */
    fun getProcessedSignalBuffer(): List<Double> {
        return posSignalBuffer.toList()
    }

    /**
     * 获取RGB信号缓冲区
     */
    fun getRGBSignalBuffer(): List<DoubleArray> {
        return rgbBuffer.toList()
    }

    /**
     * 重置处理器状态
     */
    fun reset() {
        rgbBuffer.clear()
        posSignalBuffer.clear()
        smoothingBuffer.clear()
        overlapAddBuffer.clear()
        x1 = 0.0
        x2 = 0.0
        y1 = 0.0
        y2 = 0.0
        Log.d(TAG, "POS PPG Signal processor reset")
    }
    
    /**
     * 计算RGB三通道的平均值
     */
    private fun calculateRGBAverage(pixels: IntArray, width: Int, height: Int): DoubleArray {
        var redSum = 0L
        var greenSum = 0L
        var blueSum = 0L

        for (pixel in pixels) {
            redSum += (pixel shr 16) and 0xFF
            greenSum += (pixel shr 8) and 0xFF
            blueSum += pixel and 0xFF
        }

        val totalPixels = width * height
        return doubleArrayOf(
            redSum.toDouble() / totalPixels,
            greenSum.toDouble() / totalPixels,
            blueSum.toDouble() / totalPixels
        )
    }

    /**
     * 添加RGB样本到缓冲区
     */
    private fun addToRGBBuffer(rgb: DoubleArray) {
        rgbBuffer.add(rgb)

        // 保持缓冲区大小
        if (rgbBuffer.size > SIGNAL_BUFFER_SIZE) {
            rgbBuffer.removeAt(0)
        }
    }

    /**
     * 添加POS信号到缓冲区
     */
    private fun addToPOSBuffer(value: Double) {
        posSignalBuffer.add(value)

        // 保持缓冲区大小
        if (posSignalBuffer.size > SIGNAL_BUFFER_SIZE) {
            posSignalBuffer.removeAt(0)
        }
    }
    
    /**
     * 应用POS算法 - 完全基于权威实现 (Wang et al. 2017)
     *
     * 实现与rPPG-Toolbox完全一致的POS算法：
     * 1. 滑动窗口处理
     * 2. 重叠累加 (Overlap-Adding)
     * 3. 标准的POS变换矩阵
     */
    private fun applyPOSAlgorithm(): Double? {
        val n = rgbBuffer.size - 1 // 当前时刻索引
        val m = n - windowSize + 1 // 窗口起始索引

        // 检查是否有足够的数据
        if (m < 0 || n >= rgbBuffer.size) {
            return null
        }

        // 确保重叠累加缓冲区大小正确
        while (overlapAddBuffer.size <= n) {
            overlapAddBuffer.add(0.0)
        }

        // 获取当前窗口的RGB数据 [m:n+1]
        val windowRGB = rgbBuffer.subList(m, n + 1)

        // 1. 计算窗口内RGB的平均值（用于归一化）
        val meanR = windowRGB.map { it[0] }.average()
        val meanG = windowRGB.map { it[1] }.average()
        val meanB = windowRGB.map { it[2] }.average()

        // 避免除零
        if (meanR <= 0 || meanG <= 0 || meanB <= 0) {
            return null
        }

        // 2. 时间归一化 (Temporal Normalization)
        // Cn = RGB[m:n] / mean(RGB[m:n])
        val normalizedRGB = windowRGB.map { rgb ->
            doubleArrayOf(
                rgb[0] / meanR,
                rgb[1] / meanG,
                rgb[2] / meanB
            )
        }

        // 3. 投影 (Projection) - 应用POS变换矩阵
        // S = [[0, 1, -1], [-2, 1, 1]] * Cn^T
        val s1 = normalizedRGB.map { it[1] - it[2] } // G - B
        val s2 = normalizedRGB.map { -2 * it[0] + it[1] + it[2] } // -2R + G + B

        // 4. 调谐 (Tuning) - 2D信号转1D信号
        // h = S[0] + (std(S[0]) / std(S[1])) * S[1]
        val std1 = calculateStandardDeviation(s1)
        val std2 = calculateStandardDeviation(s2)
        val alpha = if (std2 > 0.001) std1 / std2 else 0.0

        val h = s1.zip(s2) { s1Val, s2Val -> s1Val + alpha * s2Val }

        // 5. 去均值
        val meanH = h.average()
        val hCentered = h.map { it - meanH }

        // 6. 重叠累加 (Overlap-Adding)
        // H[m:n] = H[m:n] + h
        for (i in hCentered.indices) {
            val globalIndex = m + i
            if (globalIndex < overlapAddBuffer.size) {
                overlapAddBuffer[globalIndex] += hCentered[i]
            }
        }

        // 返回当前时刻的累加结果
        return if (n < overlapAddBuffer.size) overlapAddBuffer[n] else 0.0
    }
    
    /**
     * 计算标准差 - 改进版本
     */
    private fun calculateStandardDeviation(values: List<Double>): Double {
        if (values.isEmpty()) return 0.0
        if (values.size == 1) return 0.0

        val mean = values.average()
        val variance = values.map { (it - mean).pow(2) }.average()
        return sqrt(variance).coerceAtLeast(0.001) // 避免返回0
    }

    /**
     * 应用带通滤波器
     */
    private fun applyBandpassFilter(input: Double): Double {
        val (a0, a1, a2, b1, b2) = filterCoefficients

        // IIR滤波器差分方程：y[n] = a0*x[n] + a1*x[n-1] + a2*x[n-2] - b1*y[n-1] - b2*y[n-2]
        val output = a0 * input + a1 * x1 + a2 * x2 - b1 * y1 - b2 * y2

        // 更新状态变量
        x2 = x1
        x1 = input
        y2 = y1
        y1 = output

        return output
    }
    
    /**
     * 计算Butterworth带通滤波器系数（0.75-3 Hz）- 基于POS论文参数
     */
    private fun calculateButterworthCoefficients(): FilterCoefficients {
        // 归一化频率
        val nyquist = SAMPLE_RATE / 2.0
        val lowNorm = LOW_CUTOFF / nyquist
        val highNorm = HIGH_CUTOFF / nyquist

        // 一阶Butterworth带通滤波器设计（简化版本）
        val wc1 = tan(PI * lowNorm)
        val wc2 = tan(PI * highNorm)

        // 简化的带通滤波器系数
        val k = (wc2 - wc1) / (1 + wc1 * wc2)
        val a = 1 + k

        return FilterCoefficients(
            a0 = k / a,
            a1 = 0.0,
            a2 = -k / a,
            b1 = (2 * (wc1 * wc2 - 1)) / a,
            b2 = (1 - k) / a
        )
    }
    
    /**
     * 滤波器系数数据类
     */
    private data class FilterCoefficients(
        val a0: Double,
        val a1: Double,
        val a2: Double,
        val b1: Double,
        val b2: Double
    )
    
    /**
     * 获取信号统计信息
     */
    fun getSignalStatistics(): SignalStatistics {
        if (posSignalBuffer.isEmpty()) {
            return SignalStatistics(
                mean = 0.0,
                standardDeviation = 0.0,
                variance = 0.0,
                range = 0.0,
                snr = 0.0,
                sampleCount = posSignalBuffer.size.toLong()
            )
        }

        val recentSamples = posSignalBuffer.takeLast(100)
        val mean = recentSamples.average()
        val variance = recentSamples.map { (it - mean).pow(2) }.average()
        val stdDev = sqrt(variance)
        val range = (recentSamples.maxOrNull() ?: 0.0) - (recentSamples.minOrNull() ?: 0.0)

        // 估算信噪比
        val signalPower = mean * mean
        val noisePower = variance
        val snr = if (noisePower > 0) 10 * log10(signalPower / noisePower) else 0.0

        return SignalStatistics(
            mean = mean,
            standardDeviation = stdDev,
            variance = variance,
            range = range,
            snr = snr,
            sampleCount = posSignalBuffer.size.toLong()
        )
    }

    /**
     * 检查信号质量是否适合心率检测
     */
    fun isSignalQualityGood(): Boolean {
        return posSignalBuffer.size >= windowSize && // 有足够的数据进行POS计算
               rgbBuffer.size >= windowSize
    }

    /**
     * 信号统计信息数据类
     */
    data class SignalStatistics(
        val mean: Double,
        val standardDeviation: Double,
        val variance: Double,
        val range: Double,
        val snr: Double,
        val sampleCount: Long
    )
}
