package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*

/**
 * HRV综合分析器 - 基于NeuroKit hrv实现
 * 
 * 整合时域、频域和非线性HRV指标计算，提供完整的HRV分析：
 * - 数据质量评估
 * - 多域HRV指标计算
 * - 综合健康评估
 */
class HRVAnalyzer {
    
    companion object {
        private const val TAG = "HRVAnalyzer"
        
        // 分析参数
        private const val MIN_DURATION_SECONDS = 120.0  // 最小2分钟数据
        private const val MIN_INTERVALS = 50            // 最小间隔数
        private const val QUALITY_THRESHOLD = 0.6       // 质量阈值
    }
    
    // HRV计算器
    private val intervalProcessor = IntervalProcessor()
    private val timedomainHRV = TimedomainHRV()
    private val frequencyHRV = FrequencyHRV()
    private val nonlinearHRV = NonlinearHRV()
    
    /**
     * HRV分析结果数据类
     */
    data class HRVAnalysisResult(
        val isValid: Boolean,                           // 分析是否有效
        val timeDomain: TimedomainHRV.TimedomainMetrics, // 时域指标
        val frequency: FrequencyHRV.FrequencyMetrics,    // 频域指标
        val nonlinear: NonlinearHRV.NonlinearMetrics,    // 非线性指标
        val assessment: HRVAssessment,                   // 综合评估
        val dataQuality: DataQuality,                    // 数据质量
        val recommendations: List<String>                // 建议
    ) : java.io.Serializable {
        companion object {
            fun insufficient(reason: String): HRVAnalysisResult {
                return HRVAnalysisResult(
                    isValid = false,
                    timeDomain = TimedomainHRV.TimedomainMetrics(
                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0
                    ),
                    frequency = FrequencyHRV.FrequencyMetrics(
                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0
                    ),
                    nonlinear = NonlinearHRV.NonlinearMetrics(
                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0
                    ),
                    assessment = HRVAssessment.poor(reason),
                    dataQuality = DataQuality.insufficient(reason),
                    recommendations = listOf("需要更长时间的高质量测量数据")
                )
            }
        }
    }
    
    /**
     * HRV综合评估
     */
    data class HRVAssessment(
        val overallScore: Double,       // 总体评分 (0-100)
        val stressLevel: StressLevel,   // 压力水平
        val autonomicBalance: Double,   // 自主神经平衡 (LF/HF)
        val parasympatheticActivity: Double, // 副交感神经活性
        val sympatheticActivity: Double,     // 交感神经活性
        val interpretation: String      // 解读说明
    ) : java.io.Serializable {
        companion object {
            fun poor(reason: String): HRVAssessment {
                return HRVAssessment(
                    overallScore = 0.0,
                    stressLevel = StressLevel.UNKNOWN,
                    autonomicBalance = 0.0,
                    parasympatheticActivity = 0.0,
                    sympatheticActivity = 0.0,
                    interpretation = reason
                )
            }
        }
    }
    
    /**
     * 数据质量评估
     */
    data class DataQuality(
        val score: Double,              // 质量评分 (0-1)
        val duration: Double,           // 数据时长 (秒)
        val intervalCount: Int,         // 间隔数量
        val artifactRate: Double,       // 异常值比例
        val signalQuality: Double,      // 信号质量
        val isAcceptable: Boolean,      // 是否可接受
        val issues: List<String>        // 质量问题
    ) : java.io.Serializable {
        companion object {
            fun insufficient(reason: String): DataQuality {
                return DataQuality(
                    score = 0.0, duration = 0.0, intervalCount = 0,
                    artifactRate = 1.0, signalQuality = 0.0,
                    isAcceptable = false, issues = listOf(reason)
                )
            }
        }
    }
    
    /**
     * 压力水平枚举
     */
    enum class StressLevel {
        LOW,        // 低压力
        MODERATE,   // 中等压力
        HIGH,       // 高压力
        VERY_HIGH,  // 极高压力
        UNKNOWN     // 未知
    }
    
    /**
     * 分析HRV
     */
    fun analyzeHRV(
        peaks: List<POSPeakDetector.SignalMeasurement>,
        samplingRate: Double = 30.0
    ): HRVAnalysisResult {
        
        Log.d(TAG, "Starting HRV analysis with ${peaks.size} peaks")
        
        // 1. 数据充分性检查
        val dataCheck = checkDataSufficiency(peaks, samplingRate)
        if (!dataCheck.isAcceptable) {
            return HRVAnalysisResult.insufficient(dataCheck.issues.joinToString("; "))
        }
        
        // 2. 提取和处理RR间隔
        val rrIntervals = intervalProcessor.extractRRIntervals(peaks)
        val processedIntervals = intervalProcessor.processIntervals(
            rrIntervals,
            correctArtifacts = true,
            interpolate = true,
            interpolationRate = 4
        )
        
        // 3. 质量再次检查
        if (processedIntervals.qualityScore < QUALITY_THRESHOLD) {
            return HRVAnalysisResult.insufficient("处理后数据质量不足: ${processedIntervals.qualityScore}")
        }
        
        // 4. 计算各域HRV指标
        val timeMetrics = timedomainHRV.calculate(processedIntervals.intervals)
        val freqMetrics = frequencyHRV.calculate(processedIntervals.intervals, processedIntervals.interpolatedRate)
        val nonlinearMetrics = nonlinearHRV.calculate(processedIntervals.intervals)
        
        // 5. 综合评估
        val assessment = assessOverallHRV(timeMetrics, freqMetrics, nonlinearMetrics)
        val finalDataQuality = createDataQuality(processedIntervals, dataCheck)
        val recommendations = generateRecommendations(assessment, finalDataQuality)
        
        Log.d(TAG, "HRV analysis completed. Overall score: ${assessment.overallScore}")
        
        return HRVAnalysisResult(
            isValid = true,
            timeDomain = timeMetrics,
            frequency = freqMetrics,
            nonlinear = nonlinearMetrics,
            assessment = assessment,
            dataQuality = finalDataQuality,
            recommendations = recommendations
        )
    }
    
    /**
     * 检查数据充分性
     */
    private fun checkDataSufficiency(
        peaks: List<POSPeakDetector.SignalMeasurement>,
        samplingRate: Double
    ): DataQuality {
        val issues = mutableListOf<String>()
        
        // 检查峰值数量
        if (peaks.size < MIN_INTERVALS) {
            issues.add("峰值数量不足: ${peaks.size} < $MIN_INTERVALS")
        }
        
        // 检查时长
        val duration = if (peaks.size >= 2) {
            (peaks.last().timestamp - peaks.first().timestamp) / 1000.0
        } else {
            0.0
        }
        
        if (duration < MIN_DURATION_SECONDS) {
            issues.add("测量时长不足: ${duration}s < ${MIN_DURATION_SECONDS}s")
        }
        
        val isAcceptable = issues.isEmpty()
        val score = if (isAcceptable) 1.0 else 0.0
        
        return DataQuality(
            score = score,
            duration = duration,
            intervalCount = peaks.size - 1,
            artifactRate = 0.0,
            signalQuality = 1.0,
            isAcceptable = isAcceptable,
            issues = issues
        )
    }
    
    /**
     * 综合评估HRV
     */
    private fun assessOverallHRV(
        timeMetrics: TimedomainHRV.TimedomainMetrics,
        freqMetrics: FrequencyHRV.FrequencyMetrics,
        nonlinearMetrics: NonlinearHRV.NonlinearMetrics
    ): HRVAssessment {
        
        // 计算各项评分 (0-100)
        val timeScore = assessTimedomainScore(timeMetrics)
        val freqScore = assessFrequencyScore(freqMetrics)
        val nonlinearScore = assessNonlinearScore(nonlinearMetrics)
        
        // 综合评分 (加权平均)
        val overallScore = (timeScore * 0.4 + freqScore * 0.4 + nonlinearScore * 0.2)
            .coerceIn(0.0, 100.0)
        
        // 压力水平评估
        val stressLevel = assessStressLevel(freqMetrics.lfhfRatio, timeMetrics.rmssd)
        
        // 自主神经活性
        val autonomicBalance = freqMetrics.lfhfRatio
        val parasympatheticActivity = normalizeHFPower(freqMetrics.hf)
        val sympatheticActivity = normalizeLFPower(freqMetrics.lf)
        
        // 生成解读
        val interpretation = generateInterpretation(overallScore, stressLevel, autonomicBalance)
        
        return HRVAssessment(
            overallScore = overallScore,
            stressLevel = stressLevel,
            autonomicBalance = autonomicBalance,
            parasympatheticActivity = parasympatheticActivity,
            sympatheticActivity = sympatheticActivity,
            interpretation = interpretation
        )
    }
    
    /**
     * 评估时域指标评分
     */
    private fun assessTimedomainScore(metrics: TimedomainHRV.TimedomainMetrics): Double {
        // 基于RMSSD和SDNN的评分
        val rmssdScore = when {
            metrics.rmssd >= 50 -> 100.0
            metrics.rmssd >= 30 -> 80.0
            metrics.rmssd >= 20 -> 60.0
            metrics.rmssd >= 10 -> 40.0
            else -> 20.0
        }
        
        val sdnnScore = when {
            metrics.sdNN >= 50 -> 100.0
            metrics.sdNN >= 30 -> 80.0
            metrics.sdNN >= 20 -> 60.0
            metrics.sdNN >= 10 -> 40.0
            else -> 20.0
        }
        
        return (rmssdScore + sdnnScore) / 2.0
    }
    
    /**
     * 评估频域指标评分
     */
    private fun assessFrequencyScore(metrics: FrequencyHRV.FrequencyMetrics): Double {
        // 基于总功率和LF/HF平衡的评分
        val totalPowerScore = when {
            metrics.totalPower >= 1000 -> 100.0
            metrics.totalPower >= 500 -> 80.0
            metrics.totalPower >= 200 -> 60.0
            metrics.totalPower >= 100 -> 40.0
            else -> 20.0
        }
        
        val balanceScore = when {
            metrics.lfhfRatio in 0.5..2.0 -> 100.0
            metrics.lfhfRatio in 0.3..3.0 -> 80.0
            metrics.lfhfRatio in 0.1..5.0 -> 60.0
            else -> 40.0
        }
        
        return (totalPowerScore + balanceScore) / 2.0
    }
    
    /**
     * 评估非线性指标评分
     */
    private fun assessNonlinearScore(metrics: NonlinearHRV.NonlinearMetrics): Double {
        // 基于SD1和样本熵的评分
        val sd1Score = when {
            metrics.sd1 >= 30 -> 100.0
            metrics.sd1 >= 20 -> 80.0
            metrics.sd1 >= 10 -> 60.0
            metrics.sd1 >= 5 -> 40.0
            else -> 20.0
        }
        
        val entropyScore = when {
            metrics.sampleEntropy >= 1.5 -> 100.0
            metrics.sampleEntropy >= 1.0 -> 80.0
            metrics.sampleEntropy >= 0.5 -> 60.0
            else -> 40.0
        }
        
        return (sd1Score + entropyScore) / 2.0
    }
    
    /**
     * 评估压力水平
     */
    private fun assessStressLevel(lfhfRatio: Double, rmssd: Double): StressLevel {
        return when {
            lfhfRatio > 4.0 || rmssd < 15 -> StressLevel.VERY_HIGH
            lfhfRatio > 2.5 || rmssd < 25 -> StressLevel.HIGH
            lfhfRatio > 1.5 || rmssd < 35 -> StressLevel.MODERATE
            else -> StressLevel.LOW
        }
    }
    
    /**
     * 标准化HF功率
     */
    private fun normalizeHFPower(hf: Double): Double {
        return (hf / 1000.0).coerceIn(0.0, 1.0) * 100
    }
    
    /**
     * 标准化LF功率
     */
    private fun normalizeLFPower(lf: Double): Double {
        return (lf / 1000.0).coerceIn(0.0, 1.0) * 100
    }
    
    /**
     * 生成解读说明
     */
    private fun generateInterpretation(
        overallScore: Double,
        stressLevel: StressLevel,
        autonomicBalance: Double
    ): String {
        val scoreDesc = when {
            overallScore >= 80 -> "优秀"
            overallScore >= 60 -> "良好"
            overallScore >= 40 -> "一般"
            else -> "需要改善"
        }
        
        val stressDesc = when (stressLevel) {
            StressLevel.LOW -> "压力水平较低，身心状态良好"
            StressLevel.MODERATE -> "压力水平适中，注意调节"
            StressLevel.HIGH -> "压力水平较高，建议放松"
            StressLevel.VERY_HIGH -> "压力水平很高，需要重视"
            StressLevel.UNKNOWN -> "压力水平未知"
        }
        
        val balanceDesc = when {
            autonomicBalance < 1.0 -> "副交感神经相对活跃"
            autonomicBalance > 2.0 -> "交感神经相对活跃"
            else -> "自主神经平衡良好"
        }
        
        return "HRV评分：$scoreDesc (${overallScore.toInt()}分)，$stressDesc，$balanceDesc"
    }
    
    /**
     * 创建最终数据质量评估
     */
    private fun createDataQuality(
        processedIntervals: IntervalProcessor.ProcessedIntervals,
        initialCheck: DataQuality
    ): DataQuality {
        return DataQuality(
            score = processedIntervals.qualityScore,
            duration = initialCheck.duration,
            intervalCount = processedIntervals.intervals.size,
            artifactRate = processedIntervals.artifactCount.toDouble() / processedIntervals.intervals.size,
            signalQuality = processedIntervals.qualityScore,
            isAcceptable = processedIntervals.qualityScore >= QUALITY_THRESHOLD,
            issues = if (processedIntervals.qualityScore >= QUALITY_THRESHOLD) emptyList() 
                    else listOf("数据质量不足")
        )
    }
    
    /**
     * 生成建议
     */
    private fun generateRecommendations(
        assessment: HRVAssessment,
        dataQuality: DataQuality
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 基于压力水平的建议
        when (assessment.stressLevel) {
            StressLevel.HIGH, StressLevel.VERY_HIGH -> {
                recommendations.add("建议进行深呼吸、冥想或其他放松练习")
                recommendations.add("保证充足的睡眠，避免过度劳累")
                recommendations.add("适当进行有氧运动，如散步、瑜伽")
            }
            StressLevel.MODERATE -> {
                recommendations.add("注意工作与休息的平衡")
                recommendations.add("保持规律的作息时间")
            }
            StressLevel.LOW -> {
                recommendations.add("继续保持良好的生活习惯")
                recommendations.add("可适当增加运动强度")
            }
            else -> {}
        }
        
        // 基于自主神经平衡的建议
        if (assessment.autonomicBalance > 2.5) {
            recommendations.add("交感神经过度活跃，建议减少咖啡因摄入")
        } else if (assessment.autonomicBalance < 0.5) {
            recommendations.add("可适当增加轻度运动来激活交感神经")
        }
        
        // 基于数据质量的建议
        if (dataQuality.artifactRate > 0.1) {
            recommendations.add("测量时请保持静止，避免移动和说话")
        }
        
        return recommendations
    }
}
