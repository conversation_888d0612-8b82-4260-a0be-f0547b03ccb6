package cn.flyok.bamai.ui.subscribe

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.common.ClickEffects.lightScale
import cn.flyok.bamai.ui.common.ClickEffects.mediumScale
import cn.flyok.bamai.ui.theme.BamaiNoRippleTheme

/**
 * 小红书推广弹窗组件 - 完全复刻iOS端AppXHSFView
 * 
 * 功能：
 * - 非会员用户退出订阅页面时显示
 * - 推广小红书关注
 * - 提供跳过和关注两个选项
 */
@Composable
fun XiaohongshuPromotionDialog(
    onDismiss: () -> Unit,
    onFollowClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            // 卡通人物 - 在弹窗下方（Z轴）
            Image(
                painter = painterResource(id = R.drawable.home_xhs_logo),
                contentDescription = "卡通人物",
                modifier = Modifier
                    .size(170.dp, 129.dp)
                    .offset(y = (-155).dp) // 向上偏移，部分在弹窗外
            )

            // 弹窗主体
            Box(
                modifier = Modifier
                    .size(300.dp, 230.dp)
                    .clip(RoundedCornerShape(20.dp))
                    .background(Color(0xFFFFFDFB))
            ) {
                // 弹窗内容
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.height(35.dp))
                    
                    // 文字提示图片
                    Image(
                        painter = painterResource(id = R.drawable.home_xhs2_tip),
                        contentDescription = "小红书提示",
                        modifier = Modifier.size(252.dp, 90.dp)
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 去关注按钮 - 双层设计，外层容器负责动效
                    Box(
                        modifier = Modifier
                            .lightScale(onClick = onFollowClick),
                        contentAlignment = Alignment.Center
                    ) {
                        // 按钮主体 - 包含背景和所有视觉元素
                        Box(
                            modifier = Modifier
                                .size(130.dp, 44.dp)
                                .clip(RoundedCornerShape(22.dp))
                                .background(Color(0xFFAE8772)),
                            contentAlignment = Alignment.Center
                        ) {
                            // 内层按钮
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(5.dp)
                                    .clip(RoundedCornerShape(17.dp))
                                    .background(Color(0xFFAE8772))
                                    .border(
                                        width = 1.dp,
                                        color = Color(0xFFE5D6CE),
                                        shape = RoundedCornerShape(17.dp)
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "去关注",
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = Color.White
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(22.dp))
                }
            }

            // 小红书Logo - 在弹窗顶部边缘
            Image(
                painter = painterResource(id = R.drawable.home_xhs2_logo),
                contentDescription = "小红书Logo",
                modifier = Modifier
                    .size(124.dp, 27.dp)
                    .offset(y = (-115).dp) // 在弹窗顶部边缘
            )
            
            // 关闭按钮 - 在弹窗下方，带点击缩放动效
            Image(
                painter = painterResource(id = R.drawable.home_praise_off),
                contentDescription = "关闭",
                modifier = Modifier
                    .size(50.dp)
                    .offset(y = 150.dp) // 在弹窗下方
                    .mediumScale(onClick = onDismiss)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun XiaohongshuPromotionDialogPreview() {
    BamaiNoRippleTheme {
        XiaohongshuPromotionDialog(
            onDismiss = {},
            onFollowClick = {}
        )
    }
}
