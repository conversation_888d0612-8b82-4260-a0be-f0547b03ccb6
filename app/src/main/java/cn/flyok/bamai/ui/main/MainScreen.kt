package cn.flyok.bamai.ui.main

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import cn.flyok.bamai.ui.components.BamaiTabBar
import cn.flyok.bamai.ui.components.TabPage
import cn.flyok.bamai.ui.home.HomeScreen
import cn.flyok.bamai.ui.constitution.ConstitutionScreen
import cn.flyok.bamai.ui.settings.SettingsScreen
import cn.flyok.bamai.ui.theme.BamaiNoRippleTheme

/**
 * 主界面容器 - 完全复刻iOS端的TabBar结构
 * 
 * 包含：
 * - 底部TabBar导航
 * - 三个主要页面：首页、体质、设置
 * - 全局禁用涟漪效果
 */
@Composable
fun MainScreen(
    modifier: Modifier = Modifier
) {
    var selectedTab by remember { mutableStateOf(TabPage.HOME.index) }
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 主内容区域
        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
        ) {
            when (selectedTab) {
                TabPage.HOME.index -> {
                    HomeScreen()
                }
                TabPage.CONSTITUTION.index -> {
                    ConstitutionScreen()
                }
                TabPage.SETTINGS.index -> {
                    SettingsScreen()
                }
            }
        }
        
        // 底部TabBar
        BamaiTabBar(
            selectedTab = selectedTab,
            onTabSelected = { selectedTab = it }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    BamaiNoRippleTheme {
        MainScreen()
    }
}
