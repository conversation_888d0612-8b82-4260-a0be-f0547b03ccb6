package cn.flyok.bamai.ui.home.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*

/**
 * 脉象结果卡片组件 - 完全复刻iOS端ResultTableViewCell
 * 
 * 布局结构：
 * - 白色卡片容器 (圆角20，边框)
 * - 宜忌标签区域 (红色"宜"、蓝色"忌"圆形标签)
 * - 描述文本区域 (多行文本，行间距5)
 * - 查看更多按钮 (带箭头图标)
 * - 锁定遮罩 (非订阅用户显示)
 */
@Composable
fun ResultCard(
    suitableText: String = "--",
    forbiddenText: String = "--",
    description: String = "暂时还没有数据哦，点击上方按钮，获取您的第一份脉象说明。",
    isSubscribed: Boolean = false,
    hasData: Boolean = false,
    onMoreClick: () -> Unit = {},
    onLockClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(BamaiDimensions.cardCornerRadius),
        colors = CardDefaults.cardColors(containerColor = BamaiCardBackground),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(BamaiDimensions.paddingSmall)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(BamaiDimensions.cardCornerRadius),
                colors = CardDefaults.cardColors(containerColor = BamaiCardBackground),
                border = androidx.compose.foundation.BorderStroke(
                    width = BamaiDimensions.borderWidth,
                    color = BamaiBorderColor
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(BamaiDimensions.paddingLarge)
                ) {
                    if (hasData) {
                        // 宜忌标签区域
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            // 左侧：宜标签和内容
                            Row(
                                modifier = Modifier.weight(1f),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                TagLabel(
                                    text = "宜",
                                    backgroundColor = BamaiSuitableColor,
                                    modifier = Modifier.padding(end = BamaiDimensions.paddingMedium)
                                )
                                Text(
                                    text = suitableText,
                                    style = MaterialTheme.typography.titleSmall,
                                    color = BamaiPrimaryText,
                                    modifier = Modifier.weight(1f)
                                )
                            }
                            
                            // 右侧：忌标签和内容
                            Row(
                                modifier = Modifier.weight(1f),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                TagLabel(
                                    text = "忌",
                                    backgroundColor = BamaiForbiddenColor,
                                    modifier = Modifier.padding(end = BamaiDimensions.paddingMedium)
                                )
                                Text(
                                    text = forbiddenText,
                                    style = MaterialTheme.typography.titleSmall,
                                    color = BamaiPrimaryText,
                                    modifier = Modifier.weight(1f)
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(BamaiDimensions.paddingLarge))
                    }
                    
                    // 描述文本区域
                    if (isSubscribed || !hasData) {
                        Text(
                            text = description,
                            style = MaterialTheme.typography.bodyLarge.copy(
                                lineHeight = 19.sp // 行间距5的效果
                            ),
                            color = BamaiSecondaryText,
                            modifier = Modifier.fillMaxWidth()
                        )
                        
                        if (hasData) {
                            Spacer(modifier = Modifier.height(BamaiDimensions.paddingMedium))
                            
                            // 查看更多按钮
                            TextButton(
                                onClick = onMoreClick,
                                modifier = Modifier.align(Alignment.CenterHorizontally)
                            ) {
                                Text(
                                    text = "查看更多",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = BamaiSecondaryText
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Image(
                                    painter = painterResource(id = R.drawable.home_arrow),
                                    contentDescription = "箭头",
                                    modifier = Modifier.size(BamaiDimensions.arrowIconSize)
                                )
                            }
                        }
                    } else {
                        // 非订阅用户显示锁定遮罩
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(120.dp), // 对应iOS端的targetHeight
                            contentAlignment = Alignment.Center
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.home_result_lock),
                                contentDescription = "解锁查看详情",
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(BamaiDimensions.paddingSmall),
                                contentScale = ContentScale.Fit
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 宜忌标签组件
 */
@Composable
private fun TagLabel(
    text: String,
    backgroundColor: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(BamaiDimensions.tagSize)
            .clip(CircleShape)
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.titleSmall,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 脉象数据模型 - 对应iOS端的PulseModel
 */
data class PulseData(
    val pulseName: String = "",
    val pulseFeatures: String = "",
    val suitable: List<String> = emptyList(),
    val forbidden: List<String> = emptyList(),
    val timeString: String = "",
    val pulseType: PulseType = PulseType.NONE
)

@Preview(showBackground = true)
@Composable
fun ResultCardWithDataPreview() {
    BamaiTheme {
        ResultCard(
            suitableText = "温补食物",
            forbiddenText = "生冷食物",
            description = "脉搏藏在深处，轻按不应，重按始得。多见于里证（邪气在脏腑），如气滞、血瘀、寒证、水湿停滞等。",
            isSubscribed = true,
            hasData = true,
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ResultCardNoDataPreview() {
    BamaiTheme {
        ResultCard(
            hasData = false,
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ResultCardLockedPreview() {
    BamaiTheme {
        ResultCard(
            suitableText = "温补食物",
            forbiddenText = "生冷食物",
            isSubscribed = false,
            hasData = true,
            modifier = Modifier.padding(16.dp)
        )
    }
}
