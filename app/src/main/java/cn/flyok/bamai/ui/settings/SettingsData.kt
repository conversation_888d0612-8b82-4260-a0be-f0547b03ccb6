package cn.flyok.bamai.ui.settings

import cn.flyok.bamai.R

/**
 * 用户信息数据类 - 对应iOS端的FlyUserInfo
 */
data class UserInfo(
    val id: String = "",
    val idStr: String = "",
    val type: Int = 0, // 0: 非会员, 1: 会员
    val vipEndTime: String = "",
    val vipDuration: Int = 0 // -1: 永久会员
) {
    companion object {
        fun mock(): UserInfo {
            return UserInfo(
                id = "123456789",
                idStr = "BM123456789",
                type = 0, // 默认非会员状态用于演示
                vipEndTime = "2024-12-31 23:59:59",
                vipDuration = 365
            )
        }
    }
}

/**
 * 设置项数据类 - 对应iOS端的SettingItem
 */
data class SettingItem(
    val icon: Int,
    val title: String,
    val subtitle: String? = null,
    val rightText: String? = null,
    val showArrow: Boolean = true,
    val actionType: SettingActionType
)

/**
 * 设置项操作类型 - 对应iOS端的SettingActionType
 */
enum class SettingActionType {
    IDS,           // ID
    INFO_SOURCE,   // 医疗信息来源
    USER_ZC,       // 隐私政策
    USER_XY,       // 用户协议
    ABOUT_APP      // 关于APP
}

/**
 * 圆角位置枚举 - 对应iOS端的CornerPosition
 */
enum class CornerPosition {
    TOP,       // 上圆角
    MIDDLE,    // 中间无圆角
    BOTTOM,    // 下圆角
    ALL,       // 全圆角（单独一个cell）
    NONE       // 无圆角
}

/**
 * 获取设置项分组数据
 */
fun getSettingGroups(userInfo: UserInfo): List<List<SettingItem>> {
    return listOf(
        // 第一组：ID
        listOf(
            SettingItem(
                icon = R.drawable.set_id,
                title = "ID",
                rightText = userInfo.idStr,
                showArrow = true,
                actionType = SettingActionType.IDS
            )
        ),
        // 第二组：其他设置选项
        listOf(
            SettingItem(
                icon = R.drawable.set_infosourse,
                title = "医疗信息来源",
                actionType = SettingActionType.INFO_SOURCE
            ),
            SettingItem(
                icon = R.drawable.set_userxy,
                title = "隐私政策",
                actionType = SettingActionType.USER_ZC
            ),
            SettingItem(
                icon = R.drawable.set_userzc,
                title = "用户协议",
                actionType = SettingActionType.USER_XY
            ),
            SettingItem(
                icon = R.drawable.set_app,
                title = "关于APP",
                rightText = "1.0.0",
                showArrow = false,
                actionType = SettingActionType.ABOUT_APP
            )
        )
    )
}
