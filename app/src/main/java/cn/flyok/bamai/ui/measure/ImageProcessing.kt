package cn.flyok.bamai.ui.measure

import android.graphics.ImageFormat
import android.media.Image
import java.nio.ByteBuffer

/**
 * PPG图像处理工具类
 * 移植自android-heart-rate-monitor的ImageProcessing.java
 * 用于从相机预览帧中提取红色像素值，实现PPG信号采集
 */
object ImageProcessing {
    
    /**
     * 从YUV420SP格式的图像数据中提取RGB像素数组（用于POS算法）
     *
     * @param yuv420sp YUV420SP格式的图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return RGB像素数组，每个像素包含RGB三个分量
     */
    fun decodeYUV420SPtoRGB(yuv420sp: ByteArray, width: Int, height: Int): IntArray {
        if (yuv420sp.isEmpty()) return intArrayOf()

        val frameSize = width * height
        val rgb = IntArray(frameSize)

        var yp = 0
        for (j in 0 until height) {
            var uvp = frameSize + (j shr 1) * width
            var u = 0
            var v = 0

            for (i in 0 until width) {
                val y = (0xff and yuv420sp[yp].toInt()) - 16
                if (y < 0) continue

                if ((i and 1) == 0) {
                    v = (0xff and yuv420sp[uvp++].toInt()) - 128
                    u = (0xff and yuv420sp[uvp++].toInt()) - 128
                }

                val y1192 = 1192 * y
                var r = (y1192 + 1634 * v)
                var g = (y1192 - 833 * v - 400 * u)
                var b = (y1192 + 2066 * u)

                r = if (r < 0) 0 else if (r > 262143) 262143 else r
                g = if (g < 0) 0 else if (g > 262143) 262143 else g
                b = if (b < 0) 0 else if (b > 262143) 262143 else b

                // 组合RGB值
                rgb[yp] = (0xff000000.toInt() or
                          ((r shl 6) and 0xff0000) or
                          ((g shr 2) and 0xff00) or
                          ((b shr 10) and 0xff))
                yp++
            }
        }

        return rgb
    }

    /**
     * 从YUV420SP格式的图像数据中计算红色像素的平均值
     * 这是PPG信号检测的核心算法
     *
     * @param yuv420sp YUV420SP格式的图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return 红色像素的平均值 (0-255)
     */
    fun decodeYUV420SPtoRedAvg(yuv420sp: ByteArray, width: Int, height: Int): Int {
        if (yuv420sp.isEmpty()) return 0

        val frameSize = width * height
        val sum = decodeYUV420SPtoRedSum(yuv420sp, width, height)
        return sum / frameSize
    }
    
    /**
     * 从YUV420SP格式的图像数据中计算红色像素的总和
     * 
     * @param yuv420sp YUV420SP格式的图像数据
     * @param width 图像宽度  
     * @param height 图像高度
     * @return 红色像素的总和
     */
    private fun decodeYUV420SPtoRedSum(yuv420sp: ByteArray, width: Int, height: Int): Int {
        if (yuv420sp.isEmpty()) return 0
        
        val frameSize = width * height
        var sum = 0
        
        var yp = 0
        for (j in 0 until height) {
            var uvp = frameSize + (j shr 1) * width
            var u = 0
            var v = 0
            
            for (i in 0 until width) {
                var y = (yuv420sp[yp].toInt() and 0xff) - 16
                if (y < 0) y = 0
                
                if ((i and 1) == 0) {
                    v = (yuv420sp[uvp++].toInt() and 0xff) - 128
                    u = (yuv420sp[uvp++].toInt() and 0xff) - 128
                }
                
                val y1192 = 1192 * y
                var r = (y1192 + 1634 * v)
                var g = (y1192 - 833 * v - 400 * u)
                var b = (y1192 + 2066 * u)
                
                if (r < 0) r = 0
                else if (r > 262143) r = 262143
                if (g < 0) g = 0
                else if (g > 262143) g = 262143
                if (b < 0) b = 0
                else if (b > 262143) b = 262143
                
                val pixel = 0xff000000.toInt() or ((r shl 6) and 0xff0000) or ((g shr 2) and 0xff00) or ((b shr 10) and 0xff)
                val red = (pixel shr 16) and 0xff
                sum += red
                
                yp++
            }
        }
        
        return sum
    }
    
    /**
     * 从Camera2 Image对象中提取YUV420SP数据
     * 
     * @param image Camera2的Image对象
     * @return YUV420SP格式的字节数组
     */
    fun imageToYUV420SP(image: Image): ByteArray? {
        if (image.format != ImageFormat.YUV_420_888) {
            return null
        }
        
        val planes = image.planes
        val yPlane = planes[0]
        val uPlane = planes[1]
        val vPlane = planes[2]
        
        val yBuffer = yPlane.buffer
        val uBuffer = uPlane.buffer
        val vBuffer = vPlane.buffer
        
        val ySize = yBuffer.remaining()
        val uSize = uBuffer.remaining()
        val vSize = vBuffer.remaining()
        
        val nv21 = ByteArray(ySize + uSize + vSize)
        
        // Y分量
        yBuffer.get(nv21, 0, ySize)
        
        // UV分量交错排列
        val uvPixelStride = uPlane.pixelStride
        if (uvPixelStride == 1) {
            uBuffer.get(nv21, ySize, uSize)
            vBuffer.get(nv21, ySize + uSize, vSize)
        } else {
            // 处理像素步长不为1的情况
            var uvIndex = ySize
            for (i in 0 until uSize) {
                nv21[uvIndex] = vBuffer.get(i)
                nv21[uvIndex + 1] = uBuffer.get(i)
                uvIndex += 2
            }
        }
        
        return nv21
    }
    
    /**
     * 从NV21格式转换为YUV420SP格式
     * NV21是Android相机常用的格式，YUV420SP是我们算法需要的格式
     * 
     * @param nv21 NV21格式的数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return YUV420SP格式的数据
     */
    fun nv21ToYUV420SP(nv21: ByteArray, width: Int, height: Int): ByteArray {
        val frameSize = width * height
        val yuv420sp = ByteArray(frameSize * 3 / 2)
        
        // Y分量直接复制
        System.arraycopy(nv21, 0, yuv420sp, 0, frameSize)
        
        // UV分量需要交换V和U的位置
        var i = 0
        while (i < frameSize / 4) {
            yuv420sp[frameSize + i * 2] = nv21[frameSize + i * 2 + 1] // U
            yuv420sp[frameSize + i * 2 + 1] = nv21[frameSize + i * 2] // V
            i++
        }
        
        return yuv420sp
    }
    
    /**
     * 计算图像的平均亮度
     * 用于判断手指是否正确放置在相机上
     * 
     * @param yuv420sp YUV420SP格式的图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return 平均亮度值 (0-255)
     */
    fun calculateAverageBrightness(yuv420sp: ByteArray, width: Int, height: Int): Int {
        if (yuv420sp.isEmpty()) return 0
        
        val frameSize = width * height
        var sum = 0
        
        // 只计算Y分量（亮度）
        for (i in 0 until frameSize) {
            sum += yuv420sp[i].toInt() and 0xff
        }
        
        return sum / frameSize
    }
    
    /**
     * 检查图像质量是否适合PPG检测
     * 
     * @param yuv420sp YUV420SP格式的图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return true表示图像质量良好，false表示需要调整
     */
    fun isImageQualityGood(yuv420sp: ByteArray, width: Int, height: Int): Boolean {
        val brightness = calculateAverageBrightness(yuv420sp, width, height)
        val redAvg = decodeYUV420SPtoRedAvg(yuv420sp, width, height)
        
        // 检查亮度是否在合适范围内（避免过暗或过亮）
        val brightnessOk = brightness in 50..200
        
        // 检查红色分量是否在有效范围内（避免饱和）
        val redOk = redAvg in 10..245
        
        return brightnessOk && redOk
    }
    
    /**
     * 从ByteBuffer中提取字节数组
     * 
     * @param buffer ByteBuffer对象
     * @return 字节数组
     */
    private fun ByteBuffer.toByteArray(): ByteArray {
        rewind()
        val data = ByteArray(remaining())
        get(data)
        return data
    }
}
