package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 今日养生卡片组件 - 完全复刻iOS端ConTodayTableViewCell
 * 
 * 特点：
 * - 显示节气信息和农历日期
 * - 显示最佳入睡和起床时间
 * - 非会员显示锁定状态
 */
@Composable
fun TodayWellnessCard(
    constitutionData: ConstitutionData,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(20.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(
                            width = 1.dp,
                            color = Color(0xFFF0E9E1),
                            shape = RoundedCornerShape(20.dp)
                        )
                        .padding(1.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(22.dp)
                    ) {
                        // 标题行
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.rl_jxly),
                                contentDescription = null,
                                modifier = Modifier.size(18.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(5.dp))
                            
                            Text(
                                text = "今日养生",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp
                                ),
                                color = Color(0xFF333333)
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(20.dp))
                        
                        // 节气信息区域
                        SolarTermSection()
                        
                        Spacer(modifier = Modifier.height(15.dp))
                        
                        // 睡眠时间区域
                        if (constitutionData.isVip) {
                            SleepTimeSection(constitutionData = constitutionData)
                        } else {
                            LockedSleepSection()
                        }
                    }
                }
            }
        }
    }
}

/**
 * 节气信息区域
 */
@Composable
private fun SolarTermSection() {
    // 模拟节气数据，实际项目中应该从农历库获取
    val currentDate = remember { Date() }
    val dateFormatter = SimpleDateFormat("yyyy年MM月dd日", Locale.CHINA)
    val formattedDate = dateFormatter.format(currentDate)
    
    Column {
        // 节气名称和日期
        Text(
            text = "立春   $formattedDate",
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 18.sp
            ),
            color = Color(0xFF333333)
        )
        
        Spacer(modifier = Modifier.height(5.dp))
        
        // 农历信息
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "正月初八  周二",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp
                ),
                color = Color(0xFF978163)
            )
            
            Text(
                text = "还有 15 天",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                ),
                color = Color(0xFF978163)
            )
        }
    }
}

/**
 * 睡眠时间区域 - 会员版本
 */
@Composable
private fun SleepTimeSection(
    constitutionData: ConstitutionData
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF0E9E1).copy(alpha = 0.6f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp, vertical = 18.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 最佳入睡时间 - 根据数据量显示内容
            SleepTimeItem(
                iconRes = R.drawable.con_sleep,
                title = "最佳入睡时间",
                time = if (constitutionData.hasEnoughData) constitutionData.sleepTime else "--"
            )

            // 最佳起床时间 - 根据数据量显示内容
            SleepTimeItem(
                iconRes = R.drawable.con_qich,
                title = "最佳起床时间",
                time = if (constitutionData.hasEnoughData) constitutionData.wakeupTime else "--"
            )
        }
    }
}

/**
 * 睡眠时间项目
 */
@Composable
private fun SleepTimeItem(
    iconRes: Int,
    title: String,
    time: String
) {
    Column(
        horizontalAlignment = Alignment.Start
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = iconRes),
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            
            Spacer(modifier = Modifier.width(5.dp))
            
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp
                ),
                color = Color(0xFF978163)
            )
        }
        
        Spacer(modifier = Modifier.height(5.dp))
        
        Text(
            text = time,
            style = MaterialTheme.typography.titleLarge.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 20.sp
            ),
            color = Color(0xFF333333),
            modifier = Modifier.padding(start = 23.dp)
        )
    }
}

/**
 * 锁定状态睡眠区域
 */
@Composable
private fun LockedSleepSection() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .clickable(
                onClick = { /* TODO: 跳转订阅页面 */ },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
    ) {
        Image(
            painter = painterResource(id = R.drawable.con_todaylocked),
            contentDescription = "解锁今日养生",
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.Fit
        )
    }
}

@Preview(showBackground = true)
@Composable
fun TodayWellnessCardVipPreview() {
    BamaiTheme {
        TodayWellnessCard(
            constitutionData = ConstitutionData.mock().copy(isVip = true),
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun TodayWellnessCardLockedPreview() {
    BamaiTheme {
        TodayWellnessCard(
            constitutionData = ConstitutionData.mock().copy(isVip = false),
            modifier = Modifier.padding(20.dp)
        )
    }
}
