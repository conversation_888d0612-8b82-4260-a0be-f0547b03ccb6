package cn.flyok.bamai.ui.theme

import androidx.compose.ui.graphics.Color

// iOS端颜色规范 - 完全复刻
val BamaiViewBackColor = Color(0xFFF5F1EC)  // KViewBackColor - 主背景色
val BamaiCardBackground = Color(0xFFFFFDFA)  // 卡片背景色
val BamaiBorderColor = Color(0xFFF0E9E1)     // 边框颜色
val BamaiPrimaryText = Color(0xFF333333)     // 主文字颜色
val BamaiSecondaryText = Color(0xFFA1968A)   // 副文字颜色
val BamaiButtonColor = Color(0xFFAE8772)     // 按钮颜色
val BamaiButtonBorder = Color(0xFFC2B19A)    // 按钮边框颜色
val BamaiTimeTitle = Color(0xFF978163)       // 时辰标题颜色

// 宜忌标签颜色
val BamaiSuitableColor = Color(0xFFA34040)   // 宜 - 红色
val BamaiForbiddenColor = Color(0xFF405EA3)  // 忌 - 蓝色

// 系统颜色保留（用于兼容）
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)