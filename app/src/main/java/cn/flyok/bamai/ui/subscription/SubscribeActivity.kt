package cn.flyok.bamai.ui.subscription

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.runtime.collectAsState
import cn.flyok.bamai.data.*
import cn.flyok.bamai.ui.theme.BamaiTheme

/**
 * 订阅页面Activity
 */
class SubscribeActivity : ComponentActivity() {
    
    companion object {
        fun createIntent(context: Context): Intent {
            return Intent(context, SubscribeActivity::class.java)
        }
    }
    
    private val viewModel: SubscriptionViewModel by viewModels {
        SubscriptionViewModelFactory(
            subscriptionManager = SubscriptionManager(
                context = this,
                userManager = UserManager.getInstance(this)
            )
        )
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            BamaiTheme {
                SubscribeScreen(
                    viewModel = viewModel,
                    onBackClick = { finish() }
                )
            }
        }
    }
}

/**
 * 订阅页面主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SubscribeScreen(
    viewModel: SubscriptionViewModel,
    onBackClick: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    
    // 页面加载时获取套餐列表
    LaunchedEffect(Unit) {
        viewModel.loadSubscriptionPlans()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "开通会员",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.Bold,
                            fontSize = 20.sp
                        ),
                        color = Color(0xFF333333)
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Text(
                            text = "×",
                            style = MaterialTheme.typography.headlineMedium,
                            color = Color(0xFF333333)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFFF8F6F2)
                )
            )
        },
        containerColor = Color(0xFFF8F6F2)
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF897457)
                        )
                    }
                }
                
                uiState.error != null -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = uiState.error ?: "未知错误",
                                color = Color(0xFF999999),
                                textAlign = TextAlign.Center
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            Button(
                                onClick = { viewModel.loadSubscriptionPlans() },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF897457)
                                )
                            ) {
                                Text("重试")
                            }
                        }
                    }
                }
                
                else -> {
                    SubscriptionContent(
                        plans = uiState.plans,
                        selectedPlan = uiState.selectedPlan,
                        selectedPaymentMethod = uiState.selectedPaymentMethod,
                        isProcessingPayment = uiState.isProcessingPayment,
                        onPlanSelected = viewModel::selectPlan,
                        onPaymentMethodSelected = viewModel::selectPaymentMethod,
                        onSubscribeClick = { viewModel.startSubscription(context) }
                    )
                }
            }
        }
    }
    
    // 处理支付结果
    LaunchedEffect(uiState.paymentResult) {
        uiState.paymentResult?.let { result ->
            when (result) {
                is PaymentResult.Success -> {
                    // TODO: 显示支付成功提示，然后关闭页面
                    onBackClick()
                }
                is PaymentResult.Failed -> {
                    // TODO: 显示支付失败提示
                }
                is PaymentResult.Cancelled -> {
                    // TODO: 显示支付取消提示
                }
            }
            viewModel.clearPaymentResult()
        }
    }
}

/**
 * 订阅内容区域
 */
@Composable
private fun SubscriptionContent(
    plans: List<SubscriptionPlan>,
    selectedPlan: SubscriptionPlan?,
    selectedPaymentMethod: PaymentMethod,
    isProcessingPayment: Boolean,
    onPlanSelected: (SubscriptionPlan) -> Unit,
    onPaymentMethodSelected: (PaymentMethod) -> Unit,
    onSubscribeClick: () -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(20.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // 页面标题和描述
        item {
            Column {
                Text(
                    text = "解锁完整体质分析",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 24.sp
                    ),
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "获得个性化养生建议，开启健康生活",
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 16.sp
                    ),
                    color = Color(0xFF666666)
                )
            }
        }
        
        // 套餐列表
        items(plans) { plan ->
            SubscriptionPlanCard(
                plan = plan,
                isSelected = selectedPlan?.id == plan.id,
                onSelected = { onPlanSelected(plan) }
            )
        }
        
        // 支付方式选择
        item {
            PaymentMethodSelector(
                selectedMethod = selectedPaymentMethod,
                onMethodSelected = onPaymentMethodSelected
            )
        }
        
        // 订阅按钮
        item {
            Button(
                onClick = onSubscribeClick,
                enabled = selectedPlan != null && !isProcessingPayment,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF897457),
                    disabledContainerColor = Color(0xFFCCCCCC)
                ),
                shape = MaterialTheme.shapes.medium
            ) {
                if (isProcessingPayment) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text(
                        text = selectedPlan?.let { "立即开通 ¥${it.price}" } ?: "选择套餐",
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        ),
                        color = Color.White
                    )
                }
            }
        }
        
        // 服务条款
        item {
            Text(
                text = "开通即表示同意《用户服务协议》和《隐私政策》",
                style = MaterialTheme.typography.bodySmall.copy(
                    fontSize = 12.sp
                ),
                color = Color(0xFF999999),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 订阅套餐卡片
 */
@Composable
private fun SubscriptionPlanCard(
    plan: SubscriptionPlan,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onSelected() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xFFF0EDE8) else Color.White
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, Color(0xFF897457))
        } else {
            BorderStroke(1.dp, Color(0xFFE5E5E5))
        },
        shape = MaterialTheme.shapes.medium
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = plan.name,
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold,
                                fontSize = 18.sp
                            ),
                            color = Color(0xFF333333)
                        )

                        if (plan.isPopular) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Surface(
                                color = Color(0xFF897457),
                                shape = MaterialTheme.shapes.small
                            ) {
                                Text(
                                    text = "推荐",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = Color.White,
                                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = plan.description,
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontSize = 14.sp
                        ),
                        color = Color(0xFF666666)
                    )
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Row(
                        verticalAlignment = Alignment.Bottom
                    ) {
                        Text(
                            text = "¥${plan.price}",
                            style = MaterialTheme.typography.headlineSmall.copy(
                                fontWeight = FontWeight.Bold,
                                fontSize = 20.sp
                            ),
                            color = Color(0xFF897457)
                        )

                        plan.originalPrice?.let { originalPrice ->
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "¥$originalPrice",
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontSize = 12.sp,
                                    textDecoration = TextDecoration.LineThrough
                                ),
                                color = Color(0xFF999999)
                            )
                        }
                    }

                    plan.discount?.let { discount ->
                        Text(
                            text = discount,
                            style = MaterialTheme.typography.labelSmall.copy(
                                fontSize = 10.sp
                            ),
                            color = Color(0xFF897457)
                        )
                    }
                }
            }

            if (plan.features.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))

                plan.features.take(3).forEach { feature ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(vertical = 2.dp)
                    ) {
                        Text(
                            text = "✓",
                            color = Color(0xFF897457),
                            style = MaterialTheme.typography.bodySmall
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = feature,
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontSize = 12.sp
                            ),
                            color = Color(0xFF666666)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 支付方式选择器
 */
@Composable
private fun PaymentMethodSelector(
    selectedMethod: PaymentMethod,
    onMethodSelected: (PaymentMethod) -> Unit
) {
    Column {
        Text(
            text = "支付方式",
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp
            ),
            color = Color(0xFF333333)
        )

        Spacer(modifier = Modifier.height(12.dp))

        PaymentMethod.values().forEach { method ->
            PaymentMethodItem(
                method = method,
                isSelected = selectedMethod == method,
                onSelected = { onMethodSelected(method) }
            )

            if (method != PaymentMethod.values().last()) {
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

/**
 * 支付方式选项
 */
@Composable
private fun PaymentMethodItem(
    method: PaymentMethod,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onSelected() },
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, Color(0xFF897457))
        } else {
            BorderStroke(1.dp, Color(0xFFE5E5E5))
        },
        shape = MaterialTheme.shapes.small
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = method.displayName,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp
                ),
                color = Color(0xFF333333)
            )

            RadioButton(
                selected = isSelected,
                onClick = onSelected,
                colors = RadioButtonDefaults.colors(
                    selectedColor = Color(0xFF897457)
                )
            )
        }
    }
}
