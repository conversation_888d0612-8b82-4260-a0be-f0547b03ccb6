package cn.flyok.bamai.ui.home.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*

/**
 * 记录列表项组件 - 完全复刻iOS端RecordTableViewCell
 * 
 * 布局结构：
 * - 白色卡片容器 (圆角20，边框，固定高度82)
 * - 脉象名称 (16pt粗体)
 * - 时间信息 (12pt灰色)
 * - 箭头图标 (18x18)
 * - 锁定图标 (非订阅用户显示)
 * - 选择图标 (编辑模式显示)
 */
@Composable
fun RecordItem(
    pulseName: String = "沉脉",
    timeText: String = "2025/07/24 10:30",
    isSubscribed: Boolean = false,
    isEditMode: Boolean = false,
    isSelected: Boolean = false,
    hasData: Boolean = true,
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(BamaiDimensions.cardCornerRadius),
        colors = CardDefaults.cardColors(containerColor = BamaiCardBackground),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(BamaiDimensions.recordItemHeight)
                .padding(BamaiDimensions.paddingSmall)
        ) {
            Card(
                modifier = Modifier.fillMaxSize(),
                shape = RoundedCornerShape(BamaiDimensions.cardCornerRadius),
                colors = CardDefaults.cardColors(containerColor = BamaiCardBackground),
                border = androidx.compose.foundation.BorderStroke(
                    width = BamaiDimensions.borderWidth,
                    color = BamaiBorderColor
                )
            ) {
                if (!hasData) {
                    // 无数据状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "暂无数据",
                            style = MaterialTheme.typography.titleMedium,
                            color = BamaiPrimaryText,
                            textAlign = TextAlign.Center
                        )
                    }
                } else {
                    // 有数据状态
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = BamaiDimensions.paddingXLarge),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 左侧文字信息
                        Column(
                            modifier = Modifier.weight(1f),
                            verticalArrangement = Arrangement.Center
                        ) {
                            // 脉象名称
                            Text(
                                text = pulseName,
                                style = MaterialTheme.typography.titleMedium,
                                color = BamaiPrimaryText,
                                modifier = Modifier.padding(bottom = 2.dp)
                            )
                            
                            // 时间信息
                            Text(
                                text = timeText,
                                style = MaterialTheme.typography.bodyMedium,
                                color = BamaiSecondaryText
                            )
                        }
                        
                        // 右侧图标区域
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // 锁定图标 (非订阅用户显示)
                            if (!isSubscribed) {
                                Image(
                                    painter = painterResource(id = R.drawable.home_cell_lock),
                                    contentDescription = "需要订阅",
                                    modifier = Modifier.size(BamaiDimensions.lockIconSize)
                                )
                            }
                            
                            // 选择图标 (编辑模式显示) 或 箭头图标 (普通模式显示)
                            if (isEditMode) {
                                Image(
                                    painter = painterResource(
                                        id = if (isSelected) R.drawable.home_xz else R.drawable.home_wxz
                                    ),
                                    contentDescription = if (isSelected) "已选择" else "未选择",
                                    modifier = Modifier.size(BamaiDimensions.lockIconSize)
                                )
                            } else {
                                Image(
                                    painter = painterResource(id = R.drawable.home_arrow),
                                    contentDescription = "查看详情",
                                    modifier = Modifier.size(BamaiDimensions.arrowIconSize)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 记录数据模型 - 对应iOS端的PulseModel
 */
data class RecordData(
    val id: String = "",
    val pulseName: String = "",
    val timeString: String = "",
    val pulseType: PulseType = PulseType.NONE,
    val isSelected: Boolean = false
)

/**
 * 完整脉象记录数据模型 - 包含完整的测量数据
 */
data class PulseRecord(
    val id: String,
    val pulseName: String,
    val timeString: String,
    val pulseType: PulseType,
    // 完整的测量结果数据
    val pulseResultModel: cn.flyok.bamai.ui.measure.PulseResultModel
) : java.io.Serializable {

    /**
     * 转换为简单的RecordData用于列表显示
     */
    fun toRecordData(): RecordData {
        return RecordData(
            id = id,
            pulseName = pulseName,
            timeString = timeString,
            pulseType = pulseType,
            isSelected = false
        )
    }
}

/**
 * 示例记录数据
 */
val sampleRecordData = listOf(
    RecordData(
        id = "1",
        pulseName = "沉脉",
        timeString = "2025/07/24 10:30",
        pulseType = PulseType.PING_MAI
    ),
    RecordData(
        id = "2",
        pulseName = "洪数脉",
        timeString = "2025/07/23 15:20",
        pulseType = PulseType.HONG_SHU_MAI
    ),
    RecordData(
        id = "3",
        pulseName = "细弱脉",
        timeString = "2025/07/22 09:15",
        pulseType = PulseType.XI_RUO_MAI
    )
)

@Preview(showBackground = true)
@Composable
fun RecordItemPreview() {
    BamaiTheme {
        RecordItem(
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun RecordItemSubscribedPreview() {
    BamaiTheme {
        RecordItem(
            pulseName = "洪数脉",
            timeText = "2025/07/23 15:20",
            isSubscribed = true,
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun RecordItemEditModePreview() {
    BamaiTheme {
        RecordItem(
            pulseName = "细弱脉",
            timeText = "2025/07/22 09:15",
            isSubscribed = true,
            isEditMode = true,
            isSelected = true,
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun RecordItemNoDataPreview() {
    BamaiTheme {
        RecordItem(
            hasData = false,
            modifier = Modifier.padding(16.dp)
        )
    }
}
