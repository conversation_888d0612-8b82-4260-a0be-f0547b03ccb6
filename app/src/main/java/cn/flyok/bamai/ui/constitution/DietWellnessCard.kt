package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.subscribe.SubscribeActivity
import cn.flyok.bamai.ui.theme.*

/**
 * 饮食养生卡片组件 - 完全复刻iOS端ConFoodTableViewCell
 * 
 * 特点：
 * - 显示菜谱推荐
 * - 显示食材推荐
 * - 非会员显示锁定状态
 * - 可点击跳转到菜谱详情页面
 */
@Composable
fun DietWellnessCard(
    constitutionData: ConstitutionData,
    onCardClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(
                onClick = onCardClick,
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(20.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(
                            width = 1.dp,
                            color = Color(0xFFF0E9E1),
                            shape = RoundedCornerShape(20.dp)
                        )
                        .padding(1.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(22.dp)
                    ) {
                        // 标题行
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.con_ysjy),
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                
                                Spacer(modifier = Modifier.width(5.dp))
                                
                                Text(
                                    text = "饮食养生",
                                    style = MaterialTheme.typography.titleMedium.copy(
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 16.sp
                                    ),
                                    color = Color(0xFF333333)
                                )
                            }
                            
                            // 右箭头
                            Image(
                                painter = painterResource(id = R.drawable.home_arrow),
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(20.dp))
                        
                        // 内容区域
                        if (constitutionData.isVip) {
                            VipDietContent(constitutionData = constitutionData)
                        } else {
                            LockedDietContent()
                        }
                    }
                }
            }
        }
    }
}

/**
 * 会员饮食内容 - 复刻iOS端VipUI逻辑
 */
@Composable
private fun VipDietContent(
    constitutionData: ConstitutionData
) {
    Column {
        // 根据数据量决定显示内容
        val recipes = if (constitutionData.hasEnoughData) constitutionData.recipes else emptyList()
        val recommendedFoods = if (constitutionData.hasEnoughData) constitutionData.recommendedFoods else emptyList()

        // 菜谱推荐
        if (recipes.isNotEmpty()) {
            Text(
                text = "菜谱推荐",
                style = MaterialTheme.typography.titleSmall.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp
                ),
                color = Color(0xFF333333)
            )
            
            Spacer(modifier = Modifier.height(10.dp))

            recipes.take(3).forEach { recipe ->
                RecipeItem(recipe = recipe)
                Spacer(modifier = Modifier.height(8.dp))
            }
        }

        // 食材推荐
        if (recommendedFoods.isNotEmpty()) {
            Spacer(modifier = Modifier.height(10.dp))
            
            Text(
                text = "食材推荐",
                style = MaterialTheme.typography.titleSmall.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp
                ),
                color = Color(0xFF333333)
            )
            
            Spacer(modifier = Modifier.height(10.dp))
            
            // 食材标签
            val chunkedFoods = recommendedFoods.chunked(4)
            chunkedFoods.forEach { rowFoods ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    rowFoods.forEach { food ->
                        Card(
                            shape = RoundedCornerShape(6.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = Color(0xFFF8F4F1)
                            ),
                            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                        ) {
                            Text(
                                text = food,
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontSize = 12.sp
                                ),
                                color = Color(0xFF333333),
                                modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
                
                if (rowFoods != chunkedFoods.last()) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

/**
 * 菜谱项目
 */
@Composable
private fun RecipeItem(
    recipe: Recipe
) {
    Column {
        Text(
            text = recipe.name,
            style = MaterialTheme.typography.bodyMedium.copy(
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp
            ),
            color = Color(0xFF333333)
        )
        
        Text(
            text = recipe.description,
            style = MaterialTheme.typography.bodySmall.copy(
                fontSize = 12.sp
            ),
            color = Color(0xFF978163)
        )
    }
}

/**
 * 锁定状态饮食内容
 */
@Composable
private fun LockedDietContent() {
    val context = LocalContext.current

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .clickable(
                onClick = {
                    // 跳转到订阅页面
                    val intent = SubscribeActivity.createIntent(context)
                    context.startActivity(intent)
                },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
    ) {
        Image(
            painter = painterResource(id = R.drawable.con_foodlocked),
            contentDescription = "解锁饮食养生",
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.Fit
        )
    }
}

@Preview(showBackground = true)
@Composable
fun DietWellnessCardVipPreview() {
    BamaiTheme {
        DietWellnessCard(
            constitutionData = ConstitutionData.mock().copy(isVip = true),
            onCardClick = { },
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun DietWellnessCardLockedPreview() {
    BamaiTheme {
        DietWellnessCard(
            constitutionData = ConstitutionData.mock().copy(isVip = false),
            onCardClick = { },
            modifier = Modifier.padding(20.dp)
        )
    }
}
