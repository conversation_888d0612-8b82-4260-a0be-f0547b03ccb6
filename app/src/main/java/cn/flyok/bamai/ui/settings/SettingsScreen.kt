package cn.flyok.bamai.ui.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import kotlinx.coroutines.launch
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.common.ClickEffects.lightScale
import cn.flyok.bamai.ui.subscribe.SubscribeActivity
import cn.flyok.bamai.ui.theme.*

/**
 * 设置页面 - 完全复刻iOS端SettingController
 *
 * 包含：
 * - 导航标题和装饰背景
 * - VIP会员卡片
 * - 分组设置选项列表
 */
@Composable
fun SettingsScreen(
    modifier: Modifier = Modifier
) {
    val userInfo = remember { mutableStateOf(UserInfo.mock()) }
    val clipboardManager = LocalClipboardManager.current
    val context = LocalContext.current
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()

    Scaffold(
        modifier = modifier.fillMaxSize(),
        snackbarHost = { SnackbarHost(snackbarHostState) },
        containerColor = BamaiViewBackColor
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
        // 导航栏区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
        ) {
            // 导航标题
            Text(
                text = "设置",
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 24.sp
                ),
                color = Color(0xFF333333),
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = 20.dp, bottom = 10.dp)
            )

            // 右上角装饰背景
            Image(
                painter = painterResource(id = R.drawable.home_zzbg),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = 15.dp)
                    .size(width = 131.dp, height = 153.dp),
                contentScale = ContentScale.Fit
            )
        }

        // 设置内容列表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 20.dp) // 减少底部边距，因为内容可以完全展示
        ) {
            // VIP会员卡片
            item {
                VipMemberCard(
                    userInfo = userInfo.value,
                    onCardClick = {
                        // 跳转到订阅页面
                        val intent = SubscribeActivity.createIntent(context)
                        context.startActivity(intent)
                    },
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 20.dp)
                )
            }

            // 设置选项分组
            val settingGroups = getSettingGroups(userInfo.value)
            itemsIndexed(settingGroups) { groupIndex, group ->
                SettingGroup(
                    items = group,
                    onItemClick = { item ->
                        handleSettingAction(item, context, clipboardManager, snackbarHostState, coroutineScope)
                    },
                    modifier = Modifier.padding(
                        horizontal = 20.dp,
                        vertical = if (groupIndex == 0) 0.dp else 24.dp // 调整组间距离为24dp
                    )
                )
            }
        }
        }
    }
}

/**
 * 处理设置项点击事件
 */
private fun handleSettingAction(
    item: SettingItem,
    context: android.content.Context,
    clipboardManager: androidx.compose.ui.platform.ClipboardManager,
    snackbarHostState: SnackbarHostState,
    coroutineScope: kotlinx.coroutines.CoroutineScope
) {
    when (item.actionType) {
        SettingActionType.IDS -> {
            // 复制用户ID到剪贴板
            clipboardManager.setText(AnnotatedString(item.rightText ?: ""))
            // 显示复制成功提示
            coroutineScope.launch {
                snackbarHostState.showSnackbar(
                    message = "ID已复制到剪贴板",
                    duration = SnackbarDuration.Short
                )
            }
        }

        SettingActionType.INFO_SOURCE -> {
            // TODO: 跳转到医疗信息来源页面
        }
        SettingActionType.USER_ZC -> {
            // TODO: 跳转到隐私政策页面
        }
        SettingActionType.USER_XY -> {
            // TODO: 跳转到用户协议页面
        }
        SettingActionType.ABOUT_APP -> {
            // 关于APP - 无操作
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    BamaiNoRippleTheme {
        SettingsScreen()
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenMemberPreview() {
    BamaiNoRippleTheme {
        SettingsScreen()
    }
}
