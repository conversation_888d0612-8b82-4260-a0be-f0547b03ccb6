package cn.flyok.bamai.ui.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.common.ClickEffects.lightScale
import cn.flyok.bamai.ui.theme.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * VIP会员卡片组件 - 完全复刻iOS端SettingHeaderView
 * 
 * 特点：
 * - 根据会员状态显示不同背景图片
 * - 会员状态：显示到期时间
 * - 非会员状态：显示开通按钮
 * - 卡片比例：670:280 (iOS端原始比例)
 */
@Composable
fun VipMemberCard(
    userInfo: UserInfo,
    onCardClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 计算卡片高度，保持iOS端的宽高比 670:280
    val screenWidth = 350.dp // 假设屏幕宽度减去边距
    val cardHeight = screenWidth * 280f / 670f
    
    // 外层容器负责动效
    Box(
        modifier = modifier
            .lightScale(onClick = onCardClick)
    ) {
        // 卡片主体 - 包含所有视觉元素
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(cardHeight)
                .clip(RoundedCornerShape(BamaiDimensions.cardCornerRadius))
        ) {
        // 背景图片
        Image(
            painter = painterResource(
                id = if (userInfo.type == 1) R.drawable.set_vipcard_yes else R.drawable.set_vipcard_no
            ),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds
        )
        
        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(start = 38.dp, top = 23.dp),
            verticalArrangement = Arrangement.Top
        ) {
            // 标题
            Text(
                text = "把脉会员",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 20.sp
                ),
                color = Color(0xFF805C4D)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 描述文本
            Text(
                text = if (userInfo.type == 1) "您已解锁全部权益！" else "开通会员，解锁全部权益！",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 12.sp
                ),
                color = Color(0xFF805C4D)
            )
            
            Spacer(modifier = Modifier.height(14.dp))
            
            // 按钮或到期时间
            if (userInfo.type == 1) {
                // 会员状态：显示到期时间
                val endTimeText = if (userInfo.vipDuration == -1) {
                    "永久会员"
                } else {
                    "${convertToChineseDateFormat(userInfo.vipEndTime)}到期"
                }
                
                Text(
                    text = endTimeText,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 12.sp
                    ),
                    color = Color(0xFF805C4D)
                )
            } else {
                // 非会员状态：显示开通按钮
                Box(
                    modifier = Modifier
                        .width(80.dp)
                        .height(30.dp)
                        .clip(RoundedCornerShape(15.dp))
                        .background(Color.Transparent),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.set_getvip),
                        contentDescription = "立即开通",
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds
                    )
                    
                    Text(
                        text = "立即开通",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Bold,
                            fontSize = 12.sp
                        ),
                        color = Color.White
                    )
                }
            }
        }
        }
    }
}

/**
 * 将后台返回的到期时间转换为中文格式
 * 从 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd"
 */
private fun convertToChineseDateFormat(dateString: String): String {
    return try {
        val inputFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val outputFormatter = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())
        val date = inputFormatter.parse(dateString)
        date?.let { outputFormatter.format(it) } ?: ""
    } catch (e: Exception) {
        ""
    }
}

@Preview(showBackground = true)
@Composable
fun VipMemberCardNonMemberPreview() {
    BamaiTheme {
        VipMemberCard(
            userInfo = UserInfo.mock().copy(type = 0),
            onCardClick = {},
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun VipMemberCardMemberPreview() {
    BamaiTheme {
        VipMemberCard(
            userInfo = UserInfo.mock().copy(
                type = 1,
                vipEndTime = "2024-12-31 23:59:59",
                vipDuration = 365
            ),
            onCardClick = {},
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun VipMemberCardPermanentPreview() {
    BamaiTheme {
        VipMemberCard(
            userInfo = UserInfo.mock().copy(
                type = 1,
                vipDuration = -1
            ),
            onCardClick = {},
            modifier = Modifier.padding(20.dp)
        )
    }
}
