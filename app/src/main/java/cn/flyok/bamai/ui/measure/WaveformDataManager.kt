package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 波形数据管理器
 * 负责收集、处理和管理实时的PPG信号数据，用于波形显示
 */
class WaveformDataManager {
    
    companion object {
        private const val TAG = "WaveformDataManager"
        
        // 波形显示配置
        private const val MAX_DISPLAY_POINTS = 1200 // 最大显示点数（40秒 * 30fps）- 确保35秒测量期间不丢失数据
        private const val PEAK_RETENTION_TIME = 40000L // 峰值保留时间（毫秒）- 40秒确保35秒测量期间不丢失
    }
    
    // 原始信号数据
    private val _rawSignalData = MutableStateFlow<List<Double>>(emptyList())
    val rawSignalData: StateFlow<List<Double>> = _rawSignalData.asStateFlow()
    
    // 处理后的信号数据
    private val _processedSignalData = MutableStateFlow<List<Double>>(emptyList())
    val processedSignalData: StateFlow<List<Double>> = _processedSignalData.asStateFlow()
    
    // 峰值数据
    private val _peakIndices = MutableStateFlow<List<Int>>(emptyList())
    val peakIndices: StateFlow<List<Int>> = _peakIndices.asStateFlow()
    
    // 当前心率
    private val _currentHeartRate = MutableStateFlow(0)
    val currentHeartRate: StateFlow<Int> = _currentHeartRate.asStateFlow()
    
    // 信号质量
    private val _signalQuality = MutableStateFlow(SignalQualityAssessment.SignalQuality.UNKNOWN)
    val signalQuality: StateFlow<SignalQualityAssessment.SignalQuality> = _signalQuality.asStateFlow()
    
    // 内部数据存储
    private val rawDataBuffer = mutableListOf<Double>()
    private val processedDataBuffer = mutableListOf<Double>()
    private val peakDataBuffer = mutableListOf<PeakData>()
    
    // 数据统计
    private var totalSamplesReceived = 0L
    private var lastUpdateTime = 0L

    // 测量时间记录
    private var measurementStartTime = 0L
    
    /**
     * 峰值数据类
     */
    private data class PeakData(
        val index: Int,
        val timestamp: Long,
        val confidence: Double
    )
    
    /**
     * 开始测量 - 记录开始时间
     */
    fun startMeasurement() {
        measurementStartTime = System.currentTimeMillis()
        Log.d(TAG, "Measurement started at: $measurementStartTime")
    }

    /**
     * 添加原始信号样本
     */
    fun addRawSample(value: Double) {
        synchronized(rawDataBuffer) {
            rawDataBuffer.add(value)
            totalSamplesReceived++
            
            // 保持缓冲区大小
            if (rawDataBuffer.size > MAX_DISPLAY_POINTS) {
                rawDataBuffer.removeAt(0)
                // 调整峰值索引
                adjustPeakIndicesForRemoval()
            }
            
            // 更新StateFlow
            _rawSignalData.value = rawDataBuffer.toList()
            
            // 只在每100个样本时输出一次日志，避免刷屏
            if (totalSamplesReceived % 100L == 0L) {
                Log.d(TAG, "Raw samples: $totalSamplesReceived")
            }
        }
    }
    
    /**
     * 添加处理后的信号样本
     */
    fun addProcessedSample(value: Double) {
        synchronized(processedDataBuffer) {
            processedDataBuffer.add(value)
            
            // 保持缓冲区大小
            if (processedDataBuffer.size > MAX_DISPLAY_POINTS) {
                processedDataBuffer.removeAt(0)
            }
            
            // 更新StateFlow
            _processedSignalData.value = processedDataBuffer.toList()
            
            // 减少日志输出频率
        }
    }
    
    /**
     * 添加峰值检测结果
     */
    fun addPeak(index: Int, confidence: Double = 1.0) {
        synchronized(peakDataBuffer) {
            val currentTime = System.currentTimeMillis()
            val peakData = PeakData(index, currentTime, confidence)
            
            peakDataBuffer.add(peakData)
            
            // 清理过期的峰值
            cleanupExpiredPeaks(currentTime)
            
            // 更新峰值索引列表
            updatePeakIndices()
        }
    }
    
    /**
     * 更新当前心率
     */
    fun updateHeartRate(heartRate: Int) {
        _currentHeartRate.value = heartRate
    }
    
    /**
     * 更新信号质量
     */
    fun updateSignalQuality(quality: SignalQualityAssessment.SignalQuality) {
        _signalQuality.value = quality
        // 只在质量发生变化时输出日志
        if (_signalQuality.value != quality) {
            Log.d(TAG, "Signal quality updated: $quality")
        }
    }
    
    /**
     * 清除所有数据
     */
    fun clearAllData() {
        synchronized(rawDataBuffer) {
            rawDataBuffer.clear()
            _rawSignalData.value = emptyList()
        }
        
        synchronized(processedDataBuffer) {
            processedDataBuffer.clear()
            _processedSignalData.value = emptyList()
        }
        
        synchronized(peakDataBuffer) {
            peakDataBuffer.clear()
            _peakIndices.value = emptyList()
        }
        
        _currentHeartRate.value = 0
        _signalQuality.value = SignalQualityAssessment.SignalQuality.UNKNOWN
        
        totalSamplesReceived = 0L
        lastUpdateTime = 0L
        measurementStartTime = 0L
        
        Log.d(TAG, "All waveform data cleared")
    }
    
    /**
     * 获取数据统计信息
     */
    fun getDataStatistics(): WaveformStatistics {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastUpdate = if (lastUpdateTime > 0) {
            currentTime - lastUpdateTime
        } else 0L
        
        return WaveformStatistics(
            totalSamples = totalSamplesReceived,
            rawDataPoints = rawDataBuffer.size,
            processedDataPoints = processedDataBuffer.size,
            peakCount = peakDataBuffer.size,
            timeSinceLastUpdate = timeSinceLastUpdate,
            currentHeartRate = _currentHeartRate.value,
            signalQuality = _signalQuality.value
        )
    }
    
    /**
     * 获取最近的信号数据用于分析
     */
    fun getRecentSignalData(sampleCount: Int = 60): List<Double> {
        return synchronized(processedDataBuffer) {
            if (processedDataBuffer.size <= sampleCount) {
                processedDataBuffer.toList()
            } else {
                processedDataBuffer.takeLast(sampleCount)
            }
        }
    }
    
    /**
     * 获取峰值间隔统计
     */
    fun getPeakIntervalStatistics(): PeakIntervalStats? {
        synchronized(peakDataBuffer) {
            if (peakDataBuffer.size < 2) return null
            
            val intervals = mutableListOf<Long>()
            for (i in 1 until peakDataBuffer.size) {
                val interval = peakDataBuffer[i].timestamp - peakDataBuffer[i-1].timestamp
                intervals.add(interval)
            }
            
            if (intervals.isEmpty()) return null
            
            val averageInterval = intervals.average()
            val minInterval = intervals.minOrNull() ?: 0L
            val maxInterval = intervals.maxOrNull() ?: 0L
            
            return PeakIntervalStats(
                averageInterval = averageInterval,
                minInterval = minInterval,
                maxInterval = maxInterval,
                intervalCount = intervals.size,
                estimatedHeartRate = if (averageInterval > 0) {
                    (60000.0 / averageInterval).toInt()
                } else 0
            )
        }
    }
    
    /**
     * 调整峰值索引（当移除数据点时）
     */
    private fun adjustPeakIndicesForRemoval() {
        synchronized(peakDataBuffer) {
            // 将所有峰值索引减1（因为移除了第一个数据点）
            peakDataBuffer.forEach { peak ->
                if (peak.index > 0) {
                    peakDataBuffer[peakDataBuffer.indexOf(peak)] = peak.copy(index = peak.index - 1)
                }
            }
            
            // 移除索引小于0的峰值
            peakDataBuffer.removeAll { it.index < 0 }
            
            updatePeakIndices()
        }
    }
    
    /**
     * 清理过期的峰值
     */
    private fun cleanupExpiredPeaks(currentTime: Long) {
        peakDataBuffer.removeAll { peak ->
            currentTime - peak.timestamp > PEAK_RETENTION_TIME
        }
    }
    
    /**
     * 更新峰值索引列表
     */
    private fun updatePeakIndices() {
        val indices = peakDataBuffer.map { it.index }.sorted()
        _peakIndices.value = indices
    }
    
    /**
     * 波形统计信息数据类
     */
    data class WaveformStatistics(
        val totalSamples: Long,
        val rawDataPoints: Int,
        val processedDataPoints: Int,
        val peakCount: Int,
        val timeSinceLastUpdate: Long,
        val currentHeartRate: Int,
        val signalQuality: SignalQualityAssessment.SignalQuality
    ) : java.io.Serializable
    
    /**
     * 峰值间隔统计数据类
     */
    data class PeakIntervalStats(
        val averageInterval: Double,
        val minInterval: Long,
        val maxInterval: Long,
        val intervalCount: Int,
        val estimatedHeartRate: Int
    )
    
    /**
     * 启用调试模式
     */
    fun enableDebugMode(enabled: Boolean) {
        // 可以在这里添加调试相关的逻辑
        Log.d(TAG, "Debug mode ${if (enabled) "enabled" else "disabled"}")
    }
    
    /**
     * 导出完整波形数据（用于脉象分析）
     */
    fun exportWaveformData(): WaveformExportData {
        Log.d(TAG, "Exporting waveform data: ${rawDataBuffer.size} raw, ${processedDataBuffer.size} processed, ${peakDataBuffer.size} peaks")

        val currentTime = System.currentTimeMillis()
        val actualMeasurementDuration = if (measurementStartTime > 0) {
            (currentTime - measurementStartTime) / 1000.0
        } else {
            // 如果没有记录开始时间，使用峰值时间范围作为备选
            val firstPeakTime = peakDataBuffer.firstOrNull()?.timestamp ?: currentTime
            val lastPeakTime = peakDataBuffer.lastOrNull()?.timestamp ?: currentTime
            (lastPeakTime - firstPeakTime) / 1000.0
        }

        Log.d(TAG, "Export: measurement duration = ${actualMeasurementDuration}s, peaks = ${peakDataBuffer.size}")

        return WaveformExportData(
            rawData = rawDataBuffer.toList(),
            processedData = processedDataBuffer.toList(),
            peaks = peakDataBuffer.map {
                WaveformExportData.PeakInfo(it.index, it.timestamp, it.confidence)
            },
            statistics = getDataStatistics(),
            exportTimestamp = currentTime,
            samplingRate = 30.0, // Android摄像头采样率
            measurementDuration = actualMeasurementDuration
        )
    }

    /**
     * 获取完整的心跳周期数据（用于脉象分析）
     */
    fun getHeartbeatCycles(): List<HeartbeatCycle> {
        if (peakDataBuffer.size < 2 || processedDataBuffer.isEmpty()) {
            return emptyList()
        }

        val cycles = mutableListOf<HeartbeatCycle>()

        for (i in 0 until peakDataBuffer.size - 1) {
            val startPeak = peakDataBuffer[i]
            val endPeak = peakDataBuffer[i + 1]

            // 确保索引在有效范围内
            if (startPeak.index < processedDataBuffer.size && endPeak.index < processedDataBuffer.size) {
                val cycleData = processedDataBuffer.subList(startPeak.index, endPeak.index)
                val duration = (endPeak.timestamp - startPeak.timestamp) / 1000.0 // 秒

                if (cycleData.isNotEmpty() && duration > 0.3 && duration < 2.0) { // 合理的心跳周期
                    cycles.add(
                        HeartbeatCycle(
                            startIndex = startPeak.index,
                            endIndex = endPeak.index,
                            duration = duration,
                            waveformData = cycleData,
                            peakValue = cycleData.maxOrNull() ?: 0.0,
                            valleyValue = cycleData.minOrNull() ?: 0.0,
                            timestamp = startPeak.timestamp
                        )
                    )
                }
            }
        }

        return cycles
    }

    /**
     * 获取平均心跳波形（用于脉象特征提取）
     */
    fun getAverageHeartbeatWaveform(): AverageWaveform? {
        val cycles = getHeartbeatCycles()
        if (cycles.size < 3) return null // 需要至少3个周期

        // 找到最常见的周期长度
        val commonLength = cycles.map { it.waveformData.size }.groupingBy { it }.eachCount().maxByOrNull { it.value }?.key ?: return null

        // 只使用长度相近的周期
        val validCycles = cycles.filter { abs(it.waveformData.size - commonLength) <= 2 }
        if (validCycles.size < 3) return null

        // 计算平均波形
        val averageWaveform = DoubleArray(commonLength) { 0.0 }
        for (cycle in validCycles) {
            for (i in 0 until minOf(cycle.waveformData.size, commonLength)) {
                averageWaveform[i] += cycle.waveformData[i]
            }
        }

        for (i in averageWaveform.indices) {
            averageWaveform[i] /= validCycles.size
        }

        return AverageWaveform(
            waveformData = averageWaveform.toList(),
            cycleCount = validCycles.size,
            averageDuration = validCycles.map { it.duration }.average(),
            quality = calculateWaveformQuality(validCycles)
        )
    }
    
    /**
     * 计算波形质量评分
     */
    private fun calculateWaveformQuality(cycles: List<HeartbeatCycle>): Double {
        if (cycles.isEmpty()) return 0.0

        // 基于周期一致性计算质量
        val durations = cycles.map { it.duration }
        val avgDuration = durations.average()
        val variance = durations.map { (it - avgDuration).pow(2.0) }.average()
        val cv = sqrt(variance) / avgDuration // 变异系数

        // 质量评分：变异系数越小，质量越高
        return (1.0 - cv.coerceAtMost(1.0)).coerceAtLeast(0.0)
    }

    /**
     * 波形导出数据类（扩展版）
     */
    data class WaveformExportData(
        val rawData: List<Double>,
        val processedData: List<Double>,
        val peaks: List<PeakInfo>,
        val statistics: WaveformStatistics,
        val exportTimestamp: Long,
        val samplingRate: Double,
        val measurementDuration: Double
    ) : java.io.Serializable {
        data class PeakInfo(
            val index: Int,
            val timestamp: Long,
            val confidence: Double
        ) : java.io.Serializable
    }

    /**
     * 心跳周期数据类
     */
    data class HeartbeatCycle(
        val startIndex: Int,
        val endIndex: Int,
        val duration: Double, // 秒
        val waveformData: List<Double>,
        val peakValue: Double,
        val valleyValue: Double,
        val timestamp: Long
    ) : java.io.Serializable {
        val amplitude: Double get() = peakValue - valleyValue
        val heartRate: Double get() = 60.0 / duration
    }

    /**
     * 平均波形数据类
     */
    data class AverageWaveform(
        val waveformData: List<Double>,
        val cycleCount: Int,
        val averageDuration: Double,
        val quality: Double
    ) : java.io.Serializable {
        val averageHeartRate: Double get() = 60.0 / averageDuration
        val peakValue: Double get() = waveformData.maxOrNull() ?: 0.0
        val valleyValue: Double get() = waveformData.minOrNull() ?: 0.0
        val amplitude: Double get() = peakValue - valleyValue
    }
}
