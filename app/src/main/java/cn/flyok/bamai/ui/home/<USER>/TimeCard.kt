package cn.flyok.bamai.ui.home.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*

/**
 * 十二时辰卡片组件 - 完全复刻iOS端TimeTableViewCell
 * 
 * 布局结构：
 * - 白色卡片容器 (圆角20，边框，固定高度110)
 * - 时辰图标区域 (43x66，垂直文字布局)
 * - 时间范围 (10pt字体)
 * - 标题 (18pt粗体)
 * - 描述 (14pt普通字体)
 * - 背景装饰图片
 */
@Composable
fun TimeCard(
    timeText: String = "23:00-01:00",
    titleText: String = "胆经当令",
    descriptionText: String = "睡前泡脚，暖身助入眠",
    timeCharacters: String = "子时",
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(BamaiDimensions.cardCornerRadius),
        colors = CardDefaults.cardColors(containerColor = BamaiCardBackground),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(BamaiDimensions.timeCardHeight)
                .padding(BamaiDimensions.paddingSmall)
        ) {
            Card(
                modifier = Modifier.fillMaxSize(),
                shape = RoundedCornerShape(BamaiDimensions.cardCornerRadius),
                colors = CardDefaults.cardColors(containerColor = BamaiCardBackground),
                border = androidx.compose.foundation.BorderStroke(
                    width = BamaiDimensions.borderWidth,
                    color = BamaiBorderColor
                )
            ) {
                Box(modifier = Modifier.fillMaxSize()) {
                    // 背景装饰图片 - 右下角
                    Image(
                        painter = painterResource(id = R.drawable.home_time_bg),
                        contentDescription = "时辰背景装饰",
                        modifier = Modifier
                            .size(width = 158.dp, height = 40.dp)
                            .align(Alignment.BottomEnd),
                        contentScale = ContentScale.Fit
                    )
                    
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(BamaiDimensions.paddingLarge),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 时辰图标区域
                        TimeIcon(
                            characters = timeCharacters,
                            modifier = Modifier.padding(end = BamaiDimensions.paddingLarge)
                        )
                        
                        // 文字信息区域
                        Column(
                            modifier = Modifier.weight(1f),
                            verticalArrangement = Arrangement.Center
                        ) {
                            // 时间范围
                            Text(
                                text = timeText,
                                style = MaterialTheme.typography.bodySmall,
                                color = BamaiSecondaryText,
                                modifier = Modifier.padding(bottom = 2.dp)
                            )
                            
                            // 标题
                            Text(
                                text = titleText,
                                style = MaterialTheme.typography.titleLarge,
                                color = BamaiTimeTitle,
                                modifier = Modifier.padding(bottom = 4.dp)
                            )
                            
                            // 描述
                            Text(
                                text = descriptionText,
                                style = MaterialTheme.typography.bodyLarge,
                                color = BamaiPrimaryText
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 时辰图标组件 - 复刻iOS端的垂直文字布局
 * 
 * 特点：
 * - 43x66尺寸
 * - 垂直排列的白色文字
 * - 背景使用home_time_sk图片
 */
@Composable
private fun TimeIcon(
    characters: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(
                width = BamaiDimensions.timeIconWidth,
                height = BamaiDimensions.timeIconHeight
            ),
        contentAlignment = Alignment.Center
    ) {
        // 背景图片
        Image(
            painter = painterResource(id = R.drawable.home_time_sk),
            contentDescription = "时辰图标背景",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Fit
        )
        
        // 垂直排列的文字 - 使用紧密排列
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            characters.forEachIndexed { index, char ->
                Text(
                    text = char.toString(),
                    style = MaterialTheme.typography.titleLarge.copy(
                        lineHeight = 16.sp // 减小行高实现紧密排列
                    ),
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * 十二时辰数据模型
 */
data class TimeData(
    val timeRange: String,
    val title: String,
    val description: String,
    val characters: String
)

/**
 * 获取当前时辰数据 - 复刻iOS端的getHomeTimeData函数
 */
fun getCurrentTimeData(): TimeData {
    // TODO: 根据当前时间计算对应的时辰
    // 这里先返回示例数据
    return TimeData(
        timeRange = "23:00-01:00",
        title = "胆经当令",
        description = "睡前泡脚，暖身助入眠",
        characters = "子时"
    )
}

/**
 * 十二时辰完整数据
 */
val twelveHoursData = listOf(
    TimeData("23:00-01:00", "胆经当令", "睡前泡脚，暖身助入眠", "子时"),
    TimeData("01:00-03:00", "肝经当令", "深度睡眠，养肝血", "丑时"),
    TimeData("03:00-05:00", "肺经当令", "熟睡中，肺部排毒", "寅时"),
    TimeData("05:00-07:00", "大肠经当令", "起床排便，清肠胃", "卯时"),
    TimeData("07:00-09:00", "胃经当令", "早餐时间，养胃气", "辰时"),
    TimeData("09:00-11:00", "脾经当令", "工作学习，健脾胃", "巳时"),
    TimeData("11:00-13:00", "心经当令", "午餐小憩，养心神", "午时"),
    TimeData("13:00-15:00", "小肠经当令", "午后工作，助消化", "未时"),
    TimeData("15:00-17:00", "膀胱经当令", "下午茶时，利水道", "申时"),
    TimeData("17:00-19:00", "肾经当令", "晚餐时间，补肾气", "酉时"),
    TimeData("19:00-21:00", "心包经当令", "散步休闲，护心脏", "戌时"),
    TimeData("21:00-23:00", "三焦经当令", "准备睡眠，调三焦", "亥时")
)

@Preview(showBackground = true)
@Composable
fun TimeCardPreview() {
    BamaiTheme {
        TimeCard(
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun TimeCardDifferentTimePreview() {
    BamaiTheme {
        TimeCard(
            timeText = "07:00-09:00",
            titleText = "胃经当令",
            descriptionText = "早餐时间，养胃气",
            timeCharacters = "辰时",
            modifier = Modifier.padding(16.dp)
        )
    }
}
