package cn.flyok.bamai.ui.measure

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
// 移除LazyColumn导入，改用Column
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 脉象分析结果展示组件
 * 
 * 专业的中医脉象分析结果展示界面
 */
@Composable
fun PulseAnalysisDisplay(
    pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult,
    waveformData: WaveformDataManager.WaveformExportData?,
    modifier: Modifier = Modifier
) {
    // 中医风格配色
    val primaryColor = Color(0xFF8B4513)      // 中医棕色
    val backgroundColor = Color(0xFFFFF8DC)    // 米色背景
    val cardColor = Color.White
    val textPrimary = Color(0xFF2F1B14)
    val textSecondary = Color(0xFF8B7355)
    val accentColor = Color(0xFFD2691E)       // 橙棕色
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 标题
        Text(
            text = "脉象分析报告",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = primaryColor,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        Text(
            text = "Traditional Chinese Medicine Pulse Analysis",
            fontSize = 14.sp,
            color = textSecondary,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 综合脉象类型
        OverallPulseTypeCard(pulseAnalysis, primaryColor, cardColor, textPrimary, accentColor)

        // 脉象五要素分析
        PulseFiveElementsCard(pulseAnalysis, cardColor, textPrimary, textSecondary)

        // 脉象特征详情
        PulseCharacteristicsCard(pulseAnalysis, cardColor, textPrimary, textSecondary)

        // 波形数据概览
        if (waveformData != null) {
            WaveformOverviewCard(waveformData, cardColor, textPrimary, textSecondary)
        }

        // 健康建议
        HealthSuggestionsCard(pulseAnalysis, primaryColor, cardColor, textPrimary, textSecondary)

        // 分析置信度
        AnalysisConfidenceCard(pulseAnalysis, cardColor, textPrimary, textSecondary)
    }
}

@Composable
private fun OverallPulseTypeCard(
    pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult,
    primaryColor: Color,
    cardColor: Color,
    textPrimary: Color,
    accentColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 脉象类型
            Text(
                text = "脉象类型",
                fontSize = 16.sp,
                color = textPrimary,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = pulseAnalysis.overallType.description,
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = primaryColor
                )

                if (pulseAnalysis.isCompoundPulse) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Box(
                        modifier = Modifier
                            .background(
                                accentColor,
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 6.dp, vertical = 2.dp)
                    ) {
                        Text(
                            text = "复合",
                            fontSize = 12.sp,
                            color = Color.White,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 脉象特点
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        Color(0xFFF5F5DC),
                        RoundedCornerShape(12.dp)
                    )
                    .padding(16.dp)
            ) {
                Column {
                    Text(
                        text = "脉象特点",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = textPrimary
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    pulseAnalysis.overallType.characteristics.forEach { characteristic ->
                        Row(
                            modifier = Modifier.padding(vertical = 2.dp)
                        ) {
                            Text("• ", color = accentColor, fontWeight = FontWeight.Bold)
                            Text(
                                text = characteristic,
                                fontSize = 13.sp,
                                color = textPrimary
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun PulseFiveElementsCard(
    pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult,
    cardColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "脉象五要素",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 五要素网格
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    PulseElementItem(
                        title = "脉率",
                        value = pulseAnalysis.pulseRate.description,
                        subtitle = pulseAnalysis.pulseRate.range,
                        textPrimary = textPrimary,
                        textSecondary = textSecondary
                    )
                    
                    PulseElementItem(
                        title = "脉力",
                        value = pulseAnalysis.pulseStrength.description,
                        subtitle = "强度",
                        textPrimary = textPrimary,
                        textSecondary = textSecondary
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    PulseElementItem(
                        title = "脉律",
                        value = pulseAnalysis.pulseRhythm.description,
                        subtitle = "节律",
                        textPrimary = textPrimary,
                        textSecondary = textSecondary
                    )
                    
                    PulseElementItem(
                        title = "脉形",
                        value = pulseAnalysis.pulseForm.description,
                        subtitle = "形态",
                        textPrimary = textPrimary,
                        textSecondary = textSecondary
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    PulseElementItem(
                        title = "脉象强度",
                        value = pulseAnalysis.pulseIntensity.description,
                        subtitle = "强度",
                        textPrimary = textPrimary,
                        textSecondary = textSecondary
                    )
                }
            }
        }
    }
}

@Composable
private fun PulseElementItem(
    title: String,
    value: String,
    subtitle: String,
    textPrimary: Color,
    textSecondary: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.width(100.dp)
    ) {
        Text(
            text = title,
            fontSize = 12.sp,
            color = textSecondary,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = textPrimary,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(2.dp))
        Text(
            text = subtitle,
            fontSize = 10.sp,
            color = textSecondary,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun PulseCharacteristicsCard(
    pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult,
    cardColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "脉象特征详情",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            pulseAnalysis.characteristics.forEach { characteristic ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 6.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .background(Color(0xFF8B4513), RoundedCornerShape(3.dp))
                            .padding(top = 6.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = characteristic,
                        fontSize = 14.sp,
                        color = textPrimary,
                        lineHeight = 20.sp
                    )
                }
            }
        }
    }
}

@Composable
private fun WaveformOverviewCard(
    waveformData: WaveformDataManager.WaveformExportData,
    cardColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "波形数据概览",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                DataOverviewItem(
                    title = "测量时长",
                    value = "${waveformData.measurementDuration.toInt()}秒",
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )
                
                DataOverviewItem(
                    title = "采样率",
                    value = "${waveformData.samplingRate.toInt()}Hz",
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )
                
                DataOverviewItem(
                    title = "心跳数",
                    value = "${waveformData.peaks.size}",
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )
                
                DataOverviewItem(
                    title = "数据点",
                    value = "${waveformData.processedData.size}",
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )
            }
        }
    }
}

@Composable
private fun DataOverviewItem(
    title: String,
    value: String,
    textPrimary: Color,
    textSecondary: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = textPrimary
        )
        Text(
            text = title,
            fontSize = 12.sp,
            color = textSecondary
        )
    }
}

@Composable
private fun HealthSuggestionsCard(
    pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult,
    primaryColor: Color,
    cardColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "健康调理建议",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            pulseAnalysis.suggestions.forEach { suggestion ->
                Row(
                    modifier = Modifier.padding(vertical = 6.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .background(primaryColor, RoundedCornerShape(3.dp))
                            .padding(top = 6.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = suggestion,
                        fontSize = 14.sp,
                        color = textPrimary,
                        lineHeight = 20.sp,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun AnalysisConfidenceCard(
    pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult,
    cardColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "分析可信度",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "置信度评分",
                    fontSize = 14.sp,
                    color = textPrimary
                )
                
                Text(
                    text = "${(pulseAnalysis.confidence * 100).toInt()}%",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = getConfidenceColor(pulseAnalysis.confidence)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = getConfidenceDescription(pulseAnalysis.confidence),
                fontSize = 12.sp,
                color = textSecondary,
                lineHeight = 16.sp
            )
        }
    }
}

private fun getConfidenceColor(confidence: Double): Color {
    return when {
        confidence >= 0.8 -> Color(0xFF4CAF50)
        confidence >= 0.6 -> Color(0xFFFF9800)
        confidence >= 0.4 -> Color(0xFFFF5722)
        else -> Color(0xFFF44336)
    }
}

private fun getConfidenceDescription(confidence: Double): String {
    return when {
        confidence >= 0.8 -> "分析结果可信度高，建议参考"
        confidence >= 0.6 -> "分析结果可信度中等，可作参考"
        confidence >= 0.4 -> "基于有限数据的初步分析，建议延长测量时间"
        confidence >= 0.2 -> "数据较少，分析结果仅供参考"
        else -> "数据不足，建议重新测量"
    }
}
