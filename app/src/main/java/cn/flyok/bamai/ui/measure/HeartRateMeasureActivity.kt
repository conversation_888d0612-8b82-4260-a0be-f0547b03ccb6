package cn.flyok.bamai.ui.measure

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.ImageFormat
import android.hardware.camera2.*
import android.hardware.camera2.params.OutputConfiguration
import android.hardware.camera2.params.SessionConfiguration
import android.media.Image
import android.media.ImageReader
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.os.PowerManager
import android.util.Log
import android.util.Size
import android.view.Surface
import android.view.SurfaceHolder
import android.view.SurfaceView
import android.view.TextureView
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.animation.core.*
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import cn.flyok.bamai.R
import cn.flyok.bamai.data.homeDataManager
import kotlin.math.cos
import kotlin.math.sin
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import cn.flyok.bamai.ui.theme.BamaiNoRippleTheme
import cn.flyok.bamai.ui.theme.*
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 脉搏测量结果数据模型（包含HRV和脉象分析）
 */
data class PulseResultModel(
    val heartRate: Int = 0,
    val pulseType: String = "平脉", // 脉象类型
    val pulseFeatures: List<String> = emptyList(), // 脉象特征
    val diagnosis: String = "", // 证候诊断
    val suggestions: Map<String, String> = emptyMap(), // 建议（饮食、生活、运动、心情）
    val heartRateData: List<Double> = emptyList(), // 原始心率数据数组
    val timestamps: List<Long> = emptyList(), // 对应的时间戳数组
    val measurementDuration: Double = 0.0, // 测量持续时间（秒）
    val hrvAnalysis: HRVAnalyzer.HRVAnalysisResult? = null, // HRV分析结果
    val ppgSignalData: List<Double> = emptyList(), // PPG信号数据
    val detectedPeaks: List<POSPeakDetector.SignalMeasurement> = emptyList(), // 检测到的峰值
    val pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult? = null, // 脉象分析结果
    val waveformData: WaveformDataManager.WaveformExportData? = null, // 完整波形数据
    val heartbeatCycles: List<WaveformDataManager.HeartbeatCycle> = emptyList(), // 心跳周期数据
    val averageWaveform: WaveformDataManager.AverageWaveform? = null, // 平均心跳波形
    val measurementTime: String = "", // 测量时间字符串

    // iOS格式的数据字段
    val rateDescription: String = "", // 脉率描述（频率）
    val strengthDescription: String = "", // 脉力描述
    val trendDescription: String = "", // 脉势描述
    val pulseFeatureText: String = "", // 脉象特征文本描述
    val diagnosisSections: List<Map<String, String>> = emptyList(), // 证候诊断分段
    val dietAdvice: String = "", // 饮食建议
    val lifestyleAdvice: String = "", // 生活作息建议
    val exerciseAdvice: String = "", // 运动锻炼建议
    val emotionalAdvice: String = "" // 情绪建议
) : java.io.Serializable

/**
 * PPG心率测量Activity
 * 参考android-heart-rate-monitor实现基础的PPG信号采集和心跳检测
 */
class HeartRateMeasureActivity : ComponentActivity() {

    companion object {
        private const val TAG = "HeartRateMeasureActivity"
        const val EXTRA_HEART_RATE_RESULT = "heart_rate_result"

        fun createIntent(context: Context): Intent {
            return Intent(context, HeartRateMeasureActivity::class.java)
        }
    }

    // Camera2 相关
    private var cameraManager: CameraManager? = null
    private var cameraDevice: CameraDevice? = null
    private var captureSession: CameraCaptureSession? = null
    private var backgroundThread: HandlerThread? = null
    private var backgroundHandler: Handler? = null
    private var imageReader: ImageReader? = null
    private var previewSurface: Surface? = null

    // PPG检测相关
    private val heartRateDetector = HeartRateDetector()
    private val waveformDataManager = WaveformDataManager()
    private var wakeLock: PowerManager.WakeLock? = null

    // 心率检测状态
    private val heartRateState = mutableStateOf(0)
    private val measurementProgress = mutableStateOf(0f)
    private val isFlashlightOn = mutableStateOf(false)
    private val measurementStatus = mutableStateOf("请将手指放在相机上")
    private val isMeasuring = mutableStateOf(false)
    private val isFingerDetected = mutableStateOf(false)
    private val isCountingDown = mutableStateOf(false)
    private val countdownSeconds = mutableStateOf(5)

    // 波形显示状态
    private val signalQuality = mutableStateOf(SignalQualityAssessment.SignalQuality.UNKNOWN)

    // iOS风格的状态变量
    private val isStart = mutableStateOf(false)  // 对应iOS的isStart
    private val isFinish = mutableStateOf(false) // 对应iOS的isFinish
    private val heartRates = mutableListOf<Double>() // 对应iOS的heartRates数组

    // 测量结果数据（占位）
    private val resultModel = mutableStateOf<PulseResultModel?>(null)

    // 手指检测稳定性相关
    private var fingerDetectionHistory = mutableListOf<Boolean>()
    private val detectionHistorySize = 3  // 简化为3次检测结果
    private var lastStableFingerState = false

    // 测量过程中的手指移开检测（更宽松的条件）
    private var measurementFingerHistory = mutableListOf<Boolean>()
    private val measurementHistorySize = 5  // 测量中需要更多帧来确认手指移开

    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            initializeCamera()
        } else {
            Toast.makeText(this, "需要相机权限才能进行心率测量", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取WakeLock防止屏幕休眠
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(PowerManager.FULL_WAKE_LOCK, "HeartRate:WakeLock")

        // 设置心率检测回调
        setupHeartRateDetector()

        setContent {
            BamaiNoRippleTheme {
                HeartRateMeasureScreen(
                    heartRate = heartRateState.value,
                    progress = measurementProgress.value,
                    status = measurementStatus.value,
                    isFlashlightOn = isFlashlightOn.value,
                    isMeasuring = isMeasuring.value,
                    isFingerDetected = isFingerDetected.value,
                    isCountingDown = isCountingDown.value,
                    signalQuality = signalQuality.value,
                    waveformDataManager = waveformDataManager,
                    isStart = isStart.value,
                    isFinish = isFinish.value,
                    resultModel = resultModel.value,
                    onBackClick = { finish() },
                    onStartMeasurement = { startCountdown() },
                    onStopMeasurement = { stopMeasurement() }
                )
            }
        }

        checkCameraPermission()
    }

    override fun onResume() {
        super.onResume()
        wakeLock?.acquire(10*60*1000L /*10 minutes*/)
        startBackgroundThread()
    }

    override fun onPause() {
        super.onPause()
        wakeLock?.release()
        stopBackgroundThread()
        closeCamera()
    }

    private fun checkCameraPermission() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED -> {
                initializeCamera()
            }
            else -> {
                requestPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    private fun setupHeartRateDetector() {
        // 连接波形数据管理器
        heartRateDetector.setWaveformDataManager(waveformDataManager)

        heartRateDetector.setOnMeasurementComplete { result ->
            runOnUiThread {
                Log.d(TAG, "测量完成回调被调用")
                // 计算显示用的心率（取平均值用于UI显示）
                val displayHeartRate = if (result.heartRateData.isNotEmpty()) {
                    result.heartRateData.average().toInt()
                } else {
                    0
                }

                // 更新iOS风格的状态
                heartRateState.value = displayHeartRate
                measurementStatus.value = "测量完成"
                isMeasuring.value = false
                isStart.value = false
                isFinish.value = true

                // 如果没有收集到数据，生成一些模拟数据用于测试UI
                val finalHeartRateData = if (result.heartRateData.isEmpty()) {
                    generateMockHeartRateData(displayHeartRate)
                } else {
                    result.heartRateData
                }

                val finalTimestamps = if (result.timestamps.isEmpty()) {
                    generateMockTimestamps(finalHeartRateData.size)
                } else {
                    result.timestamps
                }

                // 获取HRV分析结果
                val hrvAnalysis = heartRateDetector.getHRVAnalysis()
                val ppgSignalData = heartRateDetector.getProcessedSignalData()
                val detectedPeaks = heartRateDetector.getAllDetectedPeaks()

                // 获取脉象分析结果
                Log.d("HeartRateMeasure", "Getting pulse analysis...")
                var pulseAnalysis = heartRateDetector.getPulseAnalysis()
                val waveformData = heartRateDetector.getWaveformData()
                val heartbeatCycles = heartRateDetector.getHeartbeatCycles()
                val averageWaveform = heartRateDetector.getAverageHeartbeatWaveform()

                // 脉象分析结果可能为null，这是正常的（数据不足时）

                Log.d("HeartRateMeasure", "Final pulse analysis result: ${pulseAnalysis?.overallType?.description}, confidence: ${pulseAnalysis?.confidence}")

                // 根据脉象分析生成iOS格式的数据
                val finalPulseType = pulseAnalysis?.overallType?.description ?: "平脉"

                // 生成脉象数据（频率、脉力、脉势）- iOS格式
                val pulseDataMap = generatePulseData(displayHeartRate, pulseAnalysis)

                // 生成脉象特征描述 - iOS格式
                val pulseFeatureText = generatePulseFeatures(pulseAnalysis)

                // 生成证候诊断 - iOS格式
                val diagnosisSections = generateDiagnosisSections(pulseAnalysis)

                // 生成建议信息 - iOS格式
                val suggestions = generateSuggestions(pulseAnalysis)

                // 创建测量结果模型（包含完整的分析数据）
                val pulseResultModel = PulseResultModel(
                    heartRate = displayHeartRate,
                    pulseType = finalPulseType,
                    pulseFeatures = listOf(pulseFeatureText), // 保持兼容性
                    diagnosis = diagnosisSections.joinToString("\n") { "${it["title"]}: ${it["content"]}" },
                    suggestions = suggestions,
                    heartRateData = finalHeartRateData,
                    timestamps = finalTimestamps,
                    measurementDuration = if (result.measurementDuration > 0) result.measurementDuration else 30.0,
                    hrvAnalysis = hrvAnalysis,
                    ppgSignalData = ppgSignalData,
                    detectedPeaks = detectedPeaks,
                    pulseAnalysis = pulseAnalysis,
                    waveformData = waveformData,
                    heartbeatCycles = heartbeatCycles,
                    averageWaveform = averageWaveform,
                    measurementTime = getCurrentTimeString(),

                    // iOS格式的数据字段
                    rateDescription = pulseDataMap["rateDescription"] ?: "",
                    strengthDescription = pulseDataMap["strengthDescription"] ?: "",
                    trendDescription = pulseDataMap["trendDescription"] ?: "",
                    pulseFeatureText = pulseFeatureText,
                    diagnosisSections = diagnosisSections,
                    dietAdvice = suggestions["diet"] ?: "",
                    lifestyleAdvice = suggestions["lifestyle"] ?: "",
                    exerciseAdvice = suggestions["exercise"] ?: "",
                    emotionalAdvice = suggestions["emotion"] ?: ""
                )

                // 测量完成后关闭闪光灯和摄像头
                turnOffFlashlight()
                isFlashlightOn.value = false

                // 保存完整的测量记录到HomeDataManager
                homeDataManager.addPulseRecord(pulseResultModel)
                Log.d(TAG, "测量记录已保存到HomeDataManager")

                // 记录测量数据（用于调试和后续算法分析）
                Log.d(TAG, "Measurement completed")
                Log.d(TAG, "Data points: ${result.heartRateData.size}")
                Log.d(TAG, "Duration: ${result.measurementDuration}s")
                Log.d(TAG, "Heart rate data: ${result.heartRateData}")
                Log.d(TAG, "Timestamps: ${result.timestamps}")
                Log.d(TAG, "Display heart rate (average): $displayHeartRate BPM")

                // 跳转到脉象详情页面
                Log.d(TAG, "准备跳转到PulseDetailActivity")
                try {
                    // 创建简化的数据模型，只包含基本数据，避免序列化问题
                    val simplifiedModel = PulseResultModel(
                        heartRate = pulseResultModel.heartRate,
                        pulseType = pulseResultModel.pulseType,
                        pulseFeatures = pulseResultModel.pulseFeatures,
                        diagnosis = pulseResultModel.diagnosis,
                        suggestions = pulseResultModel.suggestions,
                        heartRateData = emptyList(), // 简化：不传递大量数据
                        timestamps = emptyList(), // 简化：不传递大量数据
                        measurementDuration = pulseResultModel.measurementDuration,
                        measurementTime = pulseResultModel.measurementTime,

                        // iOS格式的数据字段
                        rateDescription = pulseResultModel.rateDescription,
                        strengthDescription = pulseResultModel.strengthDescription,
                        trendDescription = pulseResultModel.trendDescription,
                        pulseFeatureText = pulseResultModel.pulseFeatureText,
                        diagnosisSections = pulseResultModel.diagnosisSections,
                        dietAdvice = pulseResultModel.dietAdvice,
                        lifestyleAdvice = pulseResultModel.lifestyleAdvice,
                        exerciseAdvice = pulseResultModel.exerciseAdvice,
                        emotionalAdvice = pulseResultModel.emotionalAdvice
                        // 不传递复杂的对象（hrvAnalysis, pulseAnalysis, waveformData等），避免序列化问题
                    )
                    // 获取最新记录的ID（刚刚保存的记录）
                    val latestRecordId = homeDataManager.getLatestRecordId()
                    val detailIntent = PulseDetailActivity.createIntent(this@HeartRateMeasureActivity, simplifiedModel, latestRecordId)
                    Log.d(TAG, "Intent创建成功，记录ID: $latestRecordId，开始跳转")
                    startActivity(detailIntent)
                    Log.d(TAG, "startActivity调用成功")
                    finish() // 关闭当前测量页面
                    Log.d(TAG, "finish调用成功")
                } catch (e: Exception) {
                    Log.e(TAG, "跳转失败: ${e.message}", e)
                }
            }
        }

        heartRateDetector.setOnMeasurementProgress { progress ->
            runOnUiThread {
                measurementProgress.value = progress
                val percentage = (progress * 100).toInt()
                measurementStatus.value = "正在测量中... $percentage%"
            }
        }

        heartRateDetector.setOnHeartbeatStateChanged { type ->
            runOnUiThread {
                isFlashlightOn.value = type == HeartRateDetector.HeartbeatType.RED
            }
        }

        heartRateDetector.setOnSignalQualityChanged { quality ->
            runOnUiThread {
                signalQuality.value = quality
            }
        }
    }

    private fun initializeCamera() {
        cameraManager = getSystemService(Context.CAMERA_SERVICE) as CameraManager
        try {
            val cameraId = cameraManager?.cameraIdList?.get(0) ?: return

            // 获取相机特性，选择合适的预览尺寸
            val characteristics = cameraManager?.getCameraCharacteristics(cameraId)
            val map = characteristics?.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
            val previewSizes = map?.getOutputSizes(ImageFormat.YUV_420_888)

            // 选择较小的预览尺寸以提高性能
            val selectedSize = previewSizes?.find { it.width <= 640 && it.height <= 480 }
                ?: Size(640, 480)

            Log.d(TAG, "Selected preview size: ${selectedSize.width}x${selectedSize.height}")

            // 创建ImageReader用于接收相机预览帧
            imageReader = ImageReader.newInstance(
                selectedSize.width,
                selectedSize.height,
                ImageFormat.YUV_420_888,
                1
            )
            imageReader?.setOnImageAvailableListener(imageAvailableListener, backgroundHandler)

            cameraManager?.openCamera(cameraId, cameraStateCallback, backgroundHandler)
        } catch (e: CameraAccessException) {
            Log.e(TAG, "Camera access exception", e)
        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception", e)
        }
    }

    private val imageAvailableListener = ImageReader.OnImageAvailableListener { reader ->
        val image = reader.acquireLatestImage()
        image?.let {
            try {
                // 将Camera2的Image转换为YUV420SP格式
                val yuv420sp = ImageProcessing.imageToYUV420SP(it)
                yuv420sp?.let { data ->
                    // 检查图像质量和手指检测
                    val brightness = ImageProcessing.calculateAverageBrightness(data, it.width, it.height)
                    val isQualityGood = ImageProcessing.isImageQualityGood(data, it.width, it.height)

                    runOnUiThread {
                        // 检测手指是否放置正确
                        val redAvg = ImageProcessing.decodeYUV420SPtoRedAvg(data, it.width, it.height)

                        // 更严格的手指检测条件（用于开始测量）
                        val currentDetection = brightness < 80 &&       // 手指覆盖时亮度显著降低
                                             brightness > 10 &&        // 不是完全黑暗
                                             redAvg > 80 &&            // 手指有足够的红色信号
                                             redAvg < 200 &&           // 但不是过曝
                                             isQualityGood             // 图像质量合格

                        // 测量过程中的宽松检测条件（用于检测手指移开）
                        val measurementDetection = brightness < 120 &&  // 更宽松的亮度条件
                                                  brightness > 5 &&     // 不是完全黑暗
                                                  redAvg > 50 &&        // 更宽松的红色值条件
                                                  isQualityGood         // 图像质量合格

                        // 添加到历史记录（始终更新，用于监控手指移开）
                        fingerDetectionHistory.add(currentDetection)
                        if (fingerDetectionHistory.size > detectionHistorySize) {
                            fingerDetectionHistory.removeAt(0)
                        }

                        // 更严格的稳定状态判断：需要连续3次检测结果一致
                        val positiveCount = fingerDetectionHistory.count { it }
                        val stableFingerDetected = when {
                            fingerDetectionHistory.size == detectionHistorySize -> {
                                when (positiveCount) {
                                    3 -> true  // 连续3次都检测到手指
                                    0 -> false // 连续3次都没检测到手指
                                    else -> lastStableFingerState // 检测结果不一致，保持之前状态
                                }
                            }
                            else -> lastStableFingerState // 历史记录不足，保持之前状态
                        }

                        // 测量过程中检测手指移开（使用独立的历史记录和宽松条件）
                        if (isMeasuring.value || isCountingDown.value) {
                            // 添加到测量专用的历史记录
                            measurementFingerHistory.add(measurementDetection)
                            if (measurementFingerHistory.size > measurementHistorySize) {
                                measurementFingerHistory.removeAt(0)
                            }

                            // 只有当连续5帧都检测不到手指时才认为手指移开
                            if (measurementFingerHistory.size == measurementHistorySize) {
                                val measurementPositiveCount = measurementFingerHistory.count { it }
                                if (measurementPositiveCount == 0) {
                                    runOnUiThread {
                                        // 手指移开，停止测量
                                        stopMeasurement()
                                        measurementStatus.value = "检测到手指移开，测量已停止"
                                        Log.w(TAG, "Finger removed during measurement, stopping...")
                                    }
                                }
                            }
                        } else {
                            // 正常情况下的状态更新逻辑

                            // iOS风格的手指检测逻辑
                            if (!isMeasuring.value && !isCountingDown.value && !isFinish.value) {
                                // 只有状态真正改变时才更新UI
                                if (stableFingerDetected != lastStableFingerState) {
                                    lastStableFingerState = stableFingerDetected
                                    isFingerDetected.value = stableFingerDetected

                                    if (stableFingerDetected) {
                                        // iOS风格：检测到手指后立即开始测量
                                        isStart.value = true
                                        startMeasurement()
                                        measurementStatus.value = "正在测量..."
                                    } else {
                                        // 重置状态
                                        isStart.value = false
                                        measurementStatus.value = "请将手指轻轻覆盖取景器"
                                        measurementProgress.value = 0f
                                    }
                                }
                            } else {
                                // 测量过程中只更新内部状态，不更新UI（除非手指移开）
                                lastStableFingerState = stableFingerDetected
                            }
                        }


                    }

                    // 只有在正式测量状态下才处理PPG信号
                    if (isMeasuring.value) {
                        heartRateDetector.processFrame(data, it.width, it.height)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing image", e)
            } finally {
                it.close()
            }
        }
    }

    private val cameraStateCallback = object : CameraDevice.StateCallback() {
        override fun onOpened(camera: CameraDevice) {
            cameraDevice = camera
            createCameraPreviewSession()
            Log.d(TAG, "Camera opened successfully")
        }

        override fun onDisconnected(camera: CameraDevice) {
            cameraDevice?.close()
            cameraDevice = null
        }

        override fun onError(camera: CameraDevice, error: Int) {
            cameraDevice?.close()
            cameraDevice = null
            Log.e(TAG, "Camera error: $error")
        }
    }

    private fun createCameraPreviewSession() {
        try {
            val imageReaderSurface = imageReader?.surface ?: return
            val surfaces = mutableListOf<Surface>(imageReaderSurface)

            // 如果有预览Surface，也添加到会话中
            previewSurface?.let { surfaces.add(it) }

            val captureRequestBuilder = cameraDevice?.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW)
            captureRequestBuilder?.addTarget(imageReaderSurface)

            // 如果有预览Surface，也添加为目标
            previewSurface?.let { captureRequestBuilder?.addTarget(it) }

            // 进入页面就开启闪光灯，保持光照稳定
            captureRequestBuilder?.set(CaptureRequest.FLASH_MODE, CaptureRequest.FLASH_MODE_TORCH)

            cameraDevice?.createCaptureSession(
                surfaces,
                object : CameraCaptureSession.StateCallback() {
                    override fun onConfigured(session: CameraCaptureSession) {
                        captureSession = session
                        try {
                            val captureRequest = captureRequestBuilder?.build()
                            captureRequest?.let {
                                session.setRepeatingRequest(it, null, backgroundHandler)
                            }
                            // 设置闪光灯状态为开启
                            isFlashlightOn.value = true
                            Log.d(TAG, "Camera preview session created with flashlight on")
                        } catch (e: CameraAccessException) {
                            Log.e(TAG, "Failed to start camera preview", e)
                        }
                    }

                    override fun onConfigureFailed(session: CameraCaptureSession) {
                        Log.e(TAG, "Failed to configure camera session")
                    }
                },
                backgroundHandler
            )
        } catch (e: CameraAccessException) {
            Log.e(TAG, "Failed to create camera preview session", e)
        }
    }

    // 闪光灯控制方法
    private fun turnOffFlashlight() {
        try {
            val captureRequestBuilder = cameraDevice?.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW)
            val imageReaderSurface = imageReader?.surface

            if (captureRequestBuilder != null && imageReaderSurface != null) {
                captureRequestBuilder.addTarget(imageReaderSurface)
                previewSurface?.let { captureRequestBuilder.addTarget(it) }

                // 关闭闪光灯
                captureRequestBuilder.set(CaptureRequest.FLASH_MODE, CaptureRequest.FLASH_MODE_OFF)

                val captureRequest = captureRequestBuilder.build()
                captureSession?.setRepeatingRequest(captureRequest, null, backgroundHandler)

                Log.d(TAG, "Flashlight turned off")
            }
        } catch (e: CameraAccessException) {
            Log.e(TAG, "Failed to turn off flashlight", e)
        }
    }

    private fun startCountdown() {
        isCountingDown.value = true
        countdownSeconds.value = 5

        val handler = Handler(mainLooper)
        val countdownRunnable = object : Runnable {
            override fun run() {
                if (countdownSeconds.value > 0) {
                    measurementStatus.value = "即将开始测量，请保持静止 ${countdownSeconds.value}"
                    countdownSeconds.value--
                    handler.postDelayed(this, 1000)
                } else {
                    // 倒数结束，开始测量
                    isCountingDown.value = false
                    startMeasurement()
                }
            }
        }
        handler.post(countdownRunnable)
    }

    private fun startMeasurement() {
        // iOS风格的直接开始测量
        isMeasuring.value = true
        measurementStatus.value = "正在测量..."
        measurementProgress.value = 0f
        heartRateState.value = 0

        // 开始心率检测
        heartRateDetector.startDetection()
    }

    private fun stopMeasurement() {
        // iOS风格的停止测量
        isMeasuring.value = false
        isCountingDown.value = false
        isStart.value = false
        heartRateDetector.stopDetection()

        measurementProgress.value = 0f
        heartRateState.value = 0

        // 重置检测历史，重新开始检测
        fingerDetectionHistory.clear()
        measurementFingerHistory.clear()  // 重置测量专用历史记录
        lastStableFingerState = false
        isFingerDetected.value = false

        measurementStatus.value = "请将手指轻轻覆盖取景器"
    }

    private fun startBackgroundThread() {
        backgroundThread = HandlerThread("CameraBackground").also { it.start() }
        backgroundHandler = Handler(backgroundThread?.looper!!)
    }

    private fun stopBackgroundThread() {
        backgroundThread?.quitSafely()
        try {
            backgroundThread?.join()
            backgroundThread = null
            backgroundHandler = null
        } catch (e: InterruptedException) {
            Log.e(TAG, "Background thread interrupted", e)
        }
    }

    fun setPreviewSurface(surface: Surface) {
        previewSurface = surface
        // 如果相机已经打开，重新创建会话
        if (cameraDevice != null) {
            createCameraPreviewSession()
        }
    }

    private fun closeCamera() {
        heartRateDetector.stopDetection()
        captureSession?.close()
        captureSession = null
        cameraDevice?.close()
        cameraDevice = null
        imageReader?.close()
        imageReader = null
        previewSurface = null
        isFlashlightOn.value = false
    }


}

/**
 * 心率测量界面UI
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HeartRateMeasureScreen(
    heartRate: Int,
    progress: Float,
    status: String,
    isFlashlightOn: Boolean,
    isMeasuring: Boolean,
    isFingerDetected: Boolean,
    isCountingDown: Boolean,
    signalQuality: SignalQualityAssessment.SignalQuality,
    waveformDataManager: WaveformDataManager,
    isStart: Boolean,
    isFinish: Boolean,
    resultModel: PulseResultModel?,
    onBackClick: () -> Unit,
    onStartMeasurement: () -> Unit,
    onStopMeasurement: () -> Unit
) {
    // iOS风格的背景色
    val backgroundColor = Color(0xFFF8F6F1)

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 顶部导航栏 - 完全复刻iOS端设计
        CustomMeasureNavigationBar(
            onBackClick = onBackClick
        )

        // 主要内容区域 - 使用LazyColumn实现iOS TableView效果
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .background(BamaiViewBackColor), // 设置背景色与导航栏一致
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Header区域 - 包含进度条和相机预览
            item {
                MeasurementHeaderView(
                    progress = progress,
                    isStart = isStart,
                    isFinish = isFinish,
                    isMeasuring = isMeasuring,
                    isFingerDetected = isFingerDetected
                )
            }

            // 根据状态显示不同内容
            // 测量中或未开始 - 显示指导界面
            item {
                MeasurementGuideView(
                    isStart = isStart,
                    status = status
                )
            }
        }


    }
}

/**
 * 测量头部视图 - 包含进度条和相机预览
 */
@Composable
fun MeasurementHeaderView(
    progress: Float,
    isStart: Boolean,
    isFinish: Boolean,
    isMeasuring: Boolean,
    isFingerDetected: Boolean
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(290.dp)
            .background(BamaiViewBackColor), // 使用主题色，与导航栏一致
        contentAlignment = Alignment.Center
    ) {
        if (!isFinish) {
            // 圆形进度条
            CircularProgressView(
                progress = progress,
                modifier = Modifier.size(240.dp) // 稍微放大一点点
            )

            // 相机预览区域
            Box(
                modifier = Modifier
                    .size(140.dp)
                    .clip(CircleShape)
                    .background(Color.Black),
                contentAlignment = Alignment.Center
            ) {
                // 相机预览
                AndroidView(
                    factory = { context ->
                        TextureView(context).apply {
                            surfaceTextureListener = object : TextureView.SurfaceTextureListener {
                                override fun onSurfaceTextureAvailable(surface: android.graphics.SurfaceTexture, width: Int, height: Int) {
                                    val previewSurface = Surface(surface)
                                    (context as? HeartRateMeasureActivity)?.setPreviewSurface(previewSurface)
                                }

                                override fun onSurfaceTextureSizeChanged(surface: android.graphics.SurfaceTexture, width: Int, height: Int) {}
                                override fun onSurfaceTextureDestroyed(surface: android.graphics.SurfaceTexture): Boolean = true
                                override fun onSurfaceTextureUpdated(surface: android.graphics.SurfaceTexture) {}
                            }
                        }
                    },
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(CircleShape)
                )

                // 测量状态指示器
                if (isStart && !isFinish) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.align(Alignment.Center)
                    ) {
                        // 测量图标 - 白色染色
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(R.drawable.home_measure)
                                .build(),
                            contentDescription = "测量图标",
                            colorFilter = ColorFilter.tint(Color.White), // 白色染色
                            modifier = Modifier.size(48.dp)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // "正在测量..."文字，三个点有动画效果
                        val infiniteTransition = rememberInfiniteTransition(label = "dots")
                        val animatedValue by infiniteTransition.animateFloat(
                            initialValue = 0f,
                            targetValue = 3f,
                            animationSpec = infiniteRepeatable(
                                animation = tween(1200, easing = LinearEasing),
                                repeatMode = RepeatMode.Restart
                            ),
                            label = "dot_animation"
                        )

                        val dotCount = animatedValue.toInt()
                        Text(
                            text = "正在测量" + ".".repeat(dotCount + 1),
                            color = Color.White,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

/**
 * 圆形进度条组件 - 对应iOS的CircularProgressView
 */
@Composable
fun CircularProgressView(
    progress: Float,
    modifier: Modifier = Modifier
) {
    val activeColor = Color(0xFF978163)
    val inactiveColor = Color(0xFFD0C4B8) // 调整非激活颜色，增加对比度
    val tickCount = 50
    val lineWidth = 30f // 调整线条宽度
    val tickLength = 20f // 调整点的长度

    Box(
        modifier = modifier
            .drawBehind {
                drawCircularProgress(
                    progress = progress,
                    activeColor = activeColor,
                    inactiveColor = inactiveColor,
                    tickCount = tickCount,
                    lineWidth = lineWidth,
                    tickLength = tickLength
                )
            }
    )
}

/**
 * 绘制圆形进度条
 */
private fun DrawScope.drawCircularProgress(
    progress: Float,
    activeColor: Color,
    inactiveColor: Color,
    tickCount: Int,
    lineWidth: Float,
    tickLength: Float
) {
    val center = Offset(size.width / 2, size.height / 2)
    val radius = (size.minDimension / 2) - lineWidth / 2
    val startAngle = -90f // 从12点方向开始

    for (i in 0 until tickCount) {
        val angle = (2 * Math.PI * i / tickCount).toFloat()
        val isActive = (i.toFloat() / tickCount) <= progress && progress > 0

        val startPoint = Offset(
            x = center.x + (radius - lineWidth/2 - 5) * cos(angle + Math.PI * startAngle / 180).toFloat(),
            y = center.y + (radius - lineWidth/2 - 5) * sin(angle + Math.PI * startAngle / 180).toFloat()
        )

        val endPoint = Offset(
            x = center.x + (radius - lineWidth/2 - 5 - tickLength) * cos(angle + Math.PI * startAngle / 180).toFloat(),
            y = center.y + (radius - lineWidth/2 - 5 - tickLength) * sin(angle + Math.PI * startAngle / 180).toFloat()
        )

        drawLine(
            color = if (isActive) activeColor else inactiveColor,
            start = startPoint,
            end = endPoint,
            strokeWidth = 5f // 大幅增加点的粗细
        )
    }
}

/**
 * 测量指导视图 - 对应iOS的FingertipGifTableViewCell
 */
@Composable
fun MeasurementGuideView(
    isStart: Boolean,
    status: String
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 38.dp, vertical = 15.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 指导文本
        Text(
            text = "请把手指轻轻覆盖取景器\n注意在测量过程中不要放开手指",
            fontSize = 14.sp,
            color = BamaiPrimaryText, // 使用主题色
            textAlign = androidx.compose.ui.text.style.TextAlign.Center,
            lineHeight = 19.sp
        )

        Spacer(modifier = Modifier.height(40.dp)) // 增加间距，让GIF往下移

        // GIF动画区域 - 根据测量状态显示不同的GIF
        val gifResource = if (isStart) R.drawable.logogif else R.drawable.fingertipgif
        val gifSize = if (isStart) {
            Modifier.size(width = 300.dp, height = 230.dp) // 测量中的GIF尺寸
        } else {
            Modifier.size(width = 240.dp, height = 280.dp) // 指导GIF尺寸 - 缩小避免滚动
        }

        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(gifResource)
                .crossfade(true)
                .build(),
            contentDescription = if (isStart) "测量中动画" else "指导动画",
            modifier = gifSize,
            onError = {
                // 如果GIF加载失败，显示占位文本
                println("GIF加载失败: ${it.result.throwable}")
            }
        )

        // 删除实时脉象状态显示
    }
}

// 删除了实时脉象状态显示组件和迷你波形预览组件

/**
 * 生成模拟心率数据用于测试UI（当真实数据为空时）
 */
private fun generateMockHeartRateData(baseHeartRate: Int): List<Double> {
    val dataPoints = 20 // 生成20个数据点
    val mockData = mutableListOf<Double>()
    val random = kotlin.random.Random

    for (i in 0 until dataPoints) {
        // 在基础心率附近生成随机变化的数据
        val variation = random.nextDouble(-10.0, 10.0)
        val heartRate = (baseHeartRate + variation).coerceIn(50.0, 120.0)
        mockData.add(heartRate)
    }

    return mockData
}

/**
 * 生成模拟时间戳数据
 */
private fun generateMockTimestamps(dataCount: Int): List<Long> {
    val currentTime = System.currentTimeMillis()
    val timestamps = mutableListOf<Long>()

    for (i in 0 until dataCount) {
        // 每个数据点间隔1.5秒
        timestamps.add(currentTime - (dataCount - i - 1) * 1500L)
    }

    return timestamps
}





















/**
 * 获取当前时间字符串
 */
fun getCurrentTimeString(): String {
    val formatter = java.text.SimpleDateFormat("yyyy/M/d HH:mm", java.util.Locale.getDefault())
    return formatter.format(java.util.Date())
}

/**
 * 生成脉象数据（频率、脉力、脉势）- iOS格式
 */
private fun generatePulseData(heartRate: Int, pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult?): Map<String, String> {
    val rateDescription = when {
        heartRate < 60 -> "偏慢"
        heartRate > 100 -> "偏快"
        heartRate > 120 -> "过快"
        else -> "正常"
    }

    val strengthDescription = pulseAnalysis?.let {
        when (it.pulseStrength) {
            PulseWaveformAnalyzer.PulseStrength.WEAK -> "偏弱"
            PulseWaveformAnalyzer.PulseStrength.STRONG -> "偏强"
            PulseWaveformAnalyzer.PulseStrength.VERY_STRONG -> "过强"
            else -> "适中"
        }
    } ?: "适中"

    val trendDescription = pulseAnalysis?.let {
        when (it.pulseRhythm) {
            PulseWaveformAnalyzer.PulseRhythm.REGULAR -> "平稳"
            PulseWaveformAnalyzer.PulseRhythm.IRREGULAR -> "不稳"
            PulseWaveformAnalyzer.PulseRhythm.INTERMITTENT -> "间歇"
            else -> "平稳"
        }
    } ?: "平稳"

    return mapOf(
        "rateDescription" to rateDescription,
        "strengthDescription" to strengthDescription,
        "trendDescription" to trendDescription
    )
}

/**
 * 生成脉象特征描述 - iOS格式，基于中医理论
 */
private fun generatePulseFeatures(pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult?): String {
    return pulseAnalysis?.let {
        when (it.overallType) {
            PulseWaveformAnalyzer.PulseType.NORMAL -> "脉来和缓有力，节律规整，一息四至，不浮不沉，不大不小，从容和缓，是健康人的脉象"

            // 虚证类复合脉象
            PulseWaveformAnalyzer.PulseType.WEAK_SLOW -> "脉来无力，至数偏慢，轻按不应，重按始得，举之无力，按之空虚"
            PulseWaveformAnalyzer.PulseType.WEAK_FAST -> "脉来急数无力，虽快而软，按之不实，多见于阴虚内热之证"
            PulseWaveformAnalyzer.PulseType.WEAK_IRREGULAR -> "脉来缓而时一止，止无定数，脉力微弱，多见于心气不足，气血衰虚"
            PulseWaveformAnalyzer.PulseType.THIN_WEAK -> "脉细如线，应指明显，脉力微弱，多见于气血两虚，阴虚劳损"

            // 实证类复合脉象
            PulseWaveformAnalyzer.PulseType.STRONG_FAST -> "脉来洪大有力，来盛去衰，如波涛汹涌，来时强盛，去时渐衰"
            PulseWaveformAnalyzer.PulseType.STRONG_SLOW -> "脉来洪大有力，至数偏慢，按之充实，多见于寒实证，阳虚寒凝"
            PulseWaveformAnalyzer.PulseType.SLIPPERY_FAST -> "脉来流利，如盘走珠，应指圆滑，往来流利，脉率偏快"

            // 气血不调类复合脉象
            PulseWaveformAnalyzer.PulseType.IRREGULAR_WEAK -> "脉来缓而时一止，止无定数，脉力微弱，多见于心气不足，气血不调"
            PulseWaveformAnalyzer.PulseType.ROUGH_SLOW -> "脉来艰涩不流利，如轻刀刮竹，至数偏慢，多见于血瘀寒凝"
            PulseWaveformAnalyzer.PulseType.ROUGH_WEAK -> "脉来艰涩，往来不利，脉力微弱，多见于精伤血少，气滞血瘀"
            PulseWaveformAnalyzer.PulseType.THIN_FAST -> "脉细如线，至数偏快，应指明显，多见于阴虚火旺，血虚内热"

            // 特殊状态类复合脉象
            PulseWaveformAnalyzer.PulseType.SLIPPERY_NORMAL -> "脉来流利，如珠走盘，往来流利，节律规整，可能怀孕或痰湿体质"
            PulseWaveformAnalyzer.PulseType.DEEP_WEAK -> "脉位较深，重按始得，脉力微弱，多见于肾阳虚，精气不足"
            PulseWaveformAnalyzer.PulseType.SLIPPERY_STRONG -> "脉来流利有力，如珠走盘，往来流利，按之充实，多见于痰湿壅盛"
            PulseWaveformAnalyzer.PulseType.IRREGULAR_FAST -> "脉来急数，时有一止，止无定数，多见于心气不宁，宜静心调养"
            PulseWaveformAnalyzer.PulseType.IRREGULAR_SLOW -> "脉来缓而时一止，止无定数，多见于心气不足，需要补气养心"

            else -> "脉来和缓，节律规整，脉象平和，身体状况良好"
        }
    } ?: "脉来和缓，节律规整，脉象平和，身体状况良好"
}

/**
 * 自定义测量页导航栏 - 完全复刻iOS端FingertipController的导航栏设计
 *
 * 特点：
 * - 使用iOS端的返回按钮图标 (nav_icon_back)
 * - 标题"一键测量"使用iOS端的字体和颜色
 * - 背景色与iOS端完全一致 (#F8F6F1)
 * - 高度和布局完全复刻iOS端
 */
@Composable
private fun CustomMeasureNavigationBar(
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // iOS端导航栏高度：状态栏(44dp) + 导航栏(44dp) = 88dp
    val statusBarHeight = 44.dp
    val navBarHeight = 44.dp

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(statusBarHeight + navBarHeight)
            .background(BamaiViewBackColor) // iOS端的背景色 #F8F6F1
    ) {
        // 导航栏内容区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(navBarHeight)
                .align(Alignment.BottomCenter)
        ) {
            // 返回按钮 - 使用iOS端的图标和布局
            Box(
                modifier = Modifier
                    .size(60.dp, 44.dp) // iOS端返回按钮区域大小
                    .align(Alignment.CenterStart)
                    .clickable(
                        onClick = onBackClick,
                        indication = null, // 禁用涟漪效果
                        interactionSource = remember { MutableInteractionSource() }
                    ),
                contentAlignment = Alignment.Center
            ) {
                androidx.compose.foundation.Image(
                    painter = painterResource(id = R.drawable.nav_icon_back),
                    contentDescription = "返回",
                    modifier = Modifier.size(24.dp) // 图标大小
                )
            }

            // 标题 - 使用iOS端的字体和颜色
            Text(
                text = "一键测量",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontSize = 16.sp, // iOS端导航栏标题字体大小
                    fontWeight = FontWeight.Bold
                ),
                color = BamaiPrimaryText, // iOS端的文字颜色 #333333
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

/**
 * 生成证候诊断分段 - iOS格式
 */
private fun generateDiagnosisSections(pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult?): List<Map<String, String>> {
    return pulseAnalysis?.let {
        when (it.overallType) {
            PulseWaveformAnalyzer.PulseType.WEAK_SLOW -> listOf(
                mapOf("title" to "阳虚", "content" to "阳气不足，温煦功能减退，多见畏寒肢冷，精神不振"),
                mapOf("title" to "气虚", "content" to "元气不足，脏腑功能衰退，多见乏力气短，声低懒言")
            )
            PulseWaveformAnalyzer.PulseType.WEAK_FAST -> listOf(
                mapOf("title" to "阴虚", "content" to "阴液亏损，虚热内生，多见潮热盗汗，五心烦热"),
                mapOf("title" to "血虚", "content" to "血液亏虚，濡养不足，多见面色萎黄，头晕心悸")
            )
            PulseWaveformAnalyzer.PulseType.STRONG_FAST -> listOf(
                mapOf("title" to "实热", "content" to "热邪炽盛，正气抗邪，多见高热烦渴，面红目赤"),
                mapOf("title" to "火旺", "content" to "火热内盛，扰动心神，多见心烦失眠，口干咽燥")
            )
            PulseWaveformAnalyzer.PulseType.SLIPPERY_FAST -> listOf(
                mapOf("title" to "痰热", "content" to "痰浊内生，郁而化热，多见咳嗽痰黄，胸闷不适"),
                mapOf("title" to "湿热", "content" to "湿热蕴结，清浊不分，多见身重困倦，小便黄赤")
            )
            else -> listOf(
                mapOf("title" to "平和", "content" to "阴阳调和，气血充足，脏腑功能正常，身体健康")
            )
        }
    } ?: listOf(
        mapOf("title" to "平和", "content" to "阴阳调和，气血充足，脏腑功能正常，身体健康")
    )
}

/**
 * 生成建议信息 - iOS格式，包含分离的宜忌数据
 */
private fun generateSuggestions(pulseAnalysis: PulseWaveformAnalyzer.PulseAnalysisResult?): Map<String, String> {
    return pulseAnalysis?.let {
        when (it.overallType) {
            PulseWaveformAnalyzer.PulseType.WEAK_SLOW -> mapOf(
                "diet" to "宜温补食物：羊肉、生姜、桂圆、红枣等，忌生冷寒凉",
                "lifestyle" to "早睡晚起，避免熬夜，注意保暖，适当午休",
                "exercise" to "宜缓和运动：散步、太极、八段锦等，避免剧烈运动",
                "emotion" to "保持心情舒畅，避免过度忧思，可适当晒太阳",
                // 新增分离的宜忌数据
                "suitable" to "温补食物,适度保暖,缓和运动,心情舒畅",
                "forbidden" to "生冷寒凉,过度劳累,剧烈运动,忧思过度"
            )
            PulseWaveformAnalyzer.PulseType.WEAK_FAST -> mapOf(
                "diet" to "宜滋阴食物：银耳、百合、枸杞、梨等，忌辛辣燥热",
                "lifestyle" to "规律作息，充足睡眠，避免熬夜，保持环境清静",
                "exercise" to "宜轻柔运动：瑜伽、慢走、游泳等，避免大汗淋漓",
                "emotion" to "心平气和，避免急躁，可练习冥想或听轻音乐",
                // 新增分离的宜忌数据
                "suitable" to "滋阴食物,充足睡眠,轻柔运动,心平气和",
                "forbidden" to "辛辣燥热,熬夜晚睡,大汗淋漓,急躁易怒"
            )
            PulseWaveformAnalyzer.PulseType.STRONG_FAST -> mapOf(
                "diet" to "宜清热食物：绿豆、苦瓜、菊花茶等，忌辛辣油腻",
                "lifestyle" to "早睡早起，保持环境凉爽，多饮温开水",
                "exercise" to "适量运动：慢跑、游泳等，运动后及时补水",
                "emotion" to "保持冷静，避免激动，可进行深呼吸练习",
                // 新增分离的宜忌数据
                "suitable" to "清热食物,早睡早起,适量运动,保持冷静",
                "forbidden" to "辛辣油腻,环境燥热,剧烈运动,情绪激动"
            )
            // 虚证类复合脉象
            PulseWaveformAnalyzer.PulseType.WEAK_IRREGULAR -> mapOf(
                "diet" to "宜补气食物：人参、黄芪、山药、大枣等，忌耗气食物",
                "lifestyle" to "规律作息，避免熬夜，保持心情平静，适当午休",
                "exercise" to "宜轻缓运动：散步、太极、气功等，避免剧烈运动",
                "emotion" to "保持心境平和，避免情绪激动，可练习静坐冥想",
                "suitable" to "补气食物,规律作息,轻缓运动,心境平和",
                "forbidden" to "耗气食物,熬夜劳累,剧烈运动,情绪激动"
            )
            PulseWaveformAnalyzer.PulseType.THIN_WEAK -> mapOf(
                "diet" to "宜补血食物：红枣、桂圆、阿胶、菠菜等，忌寒凉食物",
                "lifestyle" to "充足睡眠，避免过度劳累，注意保暖，定时休息",
                "exercise" to "宜温和运动：慢走、瑜伽、八段锦等，避免大量出汗",
                "emotion" to "保持乐观心态，避免忧思过度，可听舒缓音乐",
                "suitable" to "补血食物,充足睡眠,温和运动,乐观心态",
                "forbidden" to "寒凉食物,过度劳累,大量出汗,忧思过度"
            )
            // 实证类复合脉象
            PulseWaveformAnalyzer.PulseType.STRONG_SLOW -> mapOf(
                "diet" to "宜温阳食物：羊肉、韭菜、肉桂、干姜等，忌寒凉生冷",
                "lifestyle" to "注意保暖，适当运动，避免久坐，保持温暖环境",
                "exercise" to "宜温阳运动：慢跑、爬山、太极等，促进阳气运行",
                "emotion" to "保持积极心态，避免消沉，可适当晒太阳",
                "suitable" to "温阳食物,注意保暖,温阳运动,积极心态",
                "forbidden" to "寒凉生冷,久坐不动,阴冷环境,消沉情绪"
            )
            PulseWaveformAnalyzer.PulseType.SLIPPERY_FAST -> mapOf(
                "diet" to "宜清热化痰：冬瓜、薏米、陈皮、竹叶等，忌肥甘厚味",
                "lifestyle" to "清淡饮食，保持环境通风，避免湿热环境",
                "exercise" to "宜有氧运动：游泳、慢跑、骑行等，促进代谢",
                "emotion" to "保持心情开朗，避免郁闷，可进行户外活动",
                "suitable" to "清热化痰,清淡饮食,有氧运动,心情开朗",
                "forbidden" to "肥甘厚味,湿热环境,久坐不动,郁闷情绪"
            )
            // 气血不调类复合脉象
            PulseWaveformAnalyzer.PulseType.IRREGULAR_WEAK -> mapOf(
                "diet" to "宜补气养心：人参、麦冬、五味子、酸枣仁等，忌刺激食物",
                "lifestyle" to "规律作息，避免熬夜，保持安静环境，注意休息",
                "exercise" to "宜轻柔运动：太极、气功、慢走等，避免剧烈运动",
                "emotion" to "保持心情平静，避免情绪波动，可练习静心调养",
                "suitable" to "补气养心,规律作息,轻柔运动,心情平静",
                "forbidden" to "刺激食物,熬夜劳累,剧烈运动,情绪波动"
            )
            PulseWaveformAnalyzer.PulseType.ROUGH_SLOW -> mapOf(
                "diet" to "宜活血温阳：当归、川芎、红花、桃仁等，忌寒凉食物",
                "lifestyle" to "注意保暖，适当按摩，避免久坐，促进血液循环",
                "exercise" to "宜温和运动：散步、太极、八段锦等，促进气血运行",
                "emotion" to "保持心情舒畅，避免郁闷，可进行适当娱乐",
                "suitable" to "活血温阳,注意保暖,温和运动,心情舒畅",
                "forbidden" to "寒凉食物,久坐不动,剧烈运动,郁闷情绪"
            )
            PulseWaveformAnalyzer.PulseType.ROUGH_WEAK -> mapOf(
                "diet" to "宜补血活血：阿胶、当归、红枣、黑芝麻等，忌辛辣刺激",
                "lifestyle" to "充足睡眠，避免过劳，适当按摩，保持温暖",
                "exercise" to "宜缓慢运动：慢走、瑜伽、拉伸等，避免过度运动",
                "emotion" to "保持平和心态，避免焦虑，可练习深呼吸",
                "suitable" to "补血活血,充足睡眠,缓慢运动,平和心态",
                "forbidden" to "辛辣刺激,过度劳累,过度运动,焦虑情绪"
            )
            PulseWaveformAnalyzer.PulseType.THIN_FAST -> mapOf(
                "diet" to "宜滋阴清热：银耳、百合、麦冬、石斛等，忌温燥食物",
                "lifestyle" to "规律作息，充足睡眠，保持环境清静，避免熬夜",
                "exercise" to "宜静态运动：瑜伽、冥想、慢走等，避免大汗淋漓",
                "emotion" to "保持内心宁静，避免急躁，可练习静坐",
                "suitable" to "滋阴清热,规律作息,静态运动,内心宁静",
                "forbidden" to "温燥食物,熬夜晚睡,大汗淋漓,急躁情绪"
            )
            // 特殊状态类复合脉象
            PulseWaveformAnalyzer.PulseType.SLIPPERY_NORMAL -> mapOf(
                "diet" to "宜清淡饮食：蔬菜、水果、粗粮、清汤等，忌油腻甜腻",
                "lifestyle" to "规律作息，保持环境通风，适当运动，注意调养",
                "exercise" to "宜适量运动：散步、游泳、瑜伽等，保持体重",
                "emotion" to "保持心情愉快，避免压力，可适当放松身心",
                "suitable" to "清淡饮食,规律作息,适量运动,心情愉快",
                "forbidden" to "油腻甜腻,久坐不动,过度压力,情绪紧张"
            )
            PulseWaveformAnalyzer.PulseType.DEEP_WEAK -> mapOf(
                "diet" to "宜温肾助阳：核桃、黑芝麻、羊肉、韭菜等，忌寒凉食物",
                "lifestyle" to "注意腰膝保暖，避免房事过度，早睡晚起",
                "exercise" to "宜温和运动：太极、八段锦、慢走等，避免过度出汗",
                "emotion" to "保持心情平和，避免恐惧，可适当晒太阳",
                "suitable" to "温肾助阳,腰膝保暖,温和运动,心情平和",
                "forbidden" to "寒凉食物,房事过度,过度出汗,恐惧情绪"
            )
            PulseWaveformAnalyzer.PulseType.SLIPPERY_STRONG -> mapOf(
                "diet" to "宜化痰利湿：薏米、冬瓜、陈皮、茯苓等，忌肥甘厚味",
                "lifestyle" to "清淡饮食，保持环境干燥，适当运动，控制体重",
                "exercise" to "宜有氧运动：慢跑、游泳、骑行等，促进代谢",
                "emotion" to "保持心情开朗，避免忧思，可进行户外活动",
                "suitable" to "化痰利湿,清淡饮食,有氧运动,心情开朗",
                "forbidden" to "肥甘厚味,湿润环境,久坐不动,忧思过度"
            )
            PulseWaveformAnalyzer.PulseType.IRREGULAR_FAST -> mapOf(
                "diet" to "宜清心安神：莲子、百合、酸枣仁、龙骨等，忌刺激食物",
                "lifestyle" to "规律作息，避免熬夜，保持安静环境，注意调养",
                "exercise" to "宜轻缓运动：太极、瑜伽、慢走等，避免剧烈运动",
                "emotion" to "保持心情平静，避免激动，可练习深呼吸",
                "suitable" to "清心安神,规律作息,轻缓运动,心情平静",
                "forbidden" to "刺激食物,熬夜劳累,剧烈运动,情绪激动"
            )
            PulseWaveformAnalyzer.PulseType.IRREGULAR_SLOW -> mapOf(
                "diet" to "宜补气养心：人参、黄芪、桂圆、大枣等，忌耗气食物",
                "lifestyle" to "充足睡眠，避免过劳，保持温暖，注意调养",
                "exercise" to "宜温和运动：散步、太极、八段锦等，增强体质",
                "emotion" to "保持乐观心态，避免忧虑，可适当静心",
                "suitable" to "补气养心,充足睡眠,温和运动,乐观心态",
                "forbidden" to "耗气食物,过度劳累,剧烈运动,忧虑情绪"
            )
            else -> mapOf(
                "diet" to "均衡饮食，多食新鲜蔬果，适量优质蛋白，少食油腻",
                "lifestyle" to "规律作息，早睡早起，保持良好的生活习惯",
                "exercise" to "适量运动，如散步、慢跑、太极等，增强体质",
                "emotion" to "保持心情愉悦，避免过度紧张，适当放松身心",
                // 新增分离的宜忌数据
                "suitable" to "均衡饮食,规律作息,适量运动,心情愉悦",
                "forbidden" to "暴饮暴食,熬夜晚睡,久坐不动,过度紧张"
            )
        }
    } ?: mapOf(
        "diet" to "均衡饮食，多食新鲜蔬果，适量优质蛋白，少食油腻",
        "lifestyle" to "规律作息，早睡早起，保持良好的生活习惯",
        "exercise" to "适量运动，如散步、慢跑、太极等，增强体质",
        "emotion" to "保持心情愉悦，避免过度紧张，适当放松身心",
        // 新增分离的宜忌数据
        "suitable" to "均衡饮食,规律作息,适量运动,心情愉悦",
        "forbidden" to "暴饮暴食,熬夜晚睡,久坐不动,过度紧张"
    )
}