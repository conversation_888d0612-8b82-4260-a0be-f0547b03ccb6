package cn.flyok.bamai.ui.measure

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import cn.flyok.bamai.R
import cn.flyok.bamai.data.homeDataManager
import cn.flyok.bamai.ui.theme.BamaiNoRippleTheme

/**
 * 脉象详情页面 - 完全移植iOS端设计
 */
class PulseDetailActivity : ComponentActivity() {
    
    companion object {
        private const val TAG = "PulseDetailActivity"
        const val EXTRA_PULSE_MODEL = "pulse_model"
        const val EXTRA_RECORD_ID = "record_id"

        fun createIntent(context: Context, pulseModel: PulseResultModel, recordId: String? = null): Intent {
            return Intent(context, PulseDetailActivity::class.java).apply {
                putExtra(EXTRA_PULSE_MODEL, pulseModel)
                recordId?.let { putExtra(EXTRA_RECORD_ID, it) }
            }
        }
    }
    
    private lateinit var pulseModel: PulseResultModel
    private var recordId: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取传入的脉象数据和记录ID
        pulseModel = intent.getSerializableExtra(EXTRA_PULSE_MODEL) as? PulseResultModel
            ?: PulseResultModel()
        recordId = intent.getStringExtra(EXTRA_RECORD_ID)

        setContent {
            BamaiNoRippleTheme {
                PulseDetailScreen(
                    pulseModel = pulseModel,
                    recordId = recordId,
                    onBackClick = { finish() },
                    onDeleteClick = {
                        // 删除记录后关闭页面
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 脉象详情主界面 - 对应iOS的PulseDetailController
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PulseDetailScreen(
    pulseModel: PulseResultModel,
    recordId: String?,
    onBackClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    // 删除确认弹窗状态
    var showDeleteDialog by remember { mutableStateOf(false) }
    // iOS风格的背景色
    val backgroundColor = Color(0xFFF8F6F1)
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        // 右上角装饰图片
        Image(
            painter = painterResource(id = R.drawable.home_zzbg),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = 15.dp)
                .size(width = 131.dp, height = 153.dp),
            contentScale = ContentScale.Fit
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding() // 添加顶部安全边距
        ) {
            // 自定义导航栏
            PulseDetailTopBar(
                onBackClick = onBackClick,
                onDeleteClick = {
                    // 显示删除确认弹窗
                    showDeleteDialog = true
                }
            )
            
            // 主要内容区域 - 使用LazyColumn实现iOS TableView效果
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(bottom = 20.dp)
            ) {
                // 8个Cell对应iOS的tableView numberOfRowsInSection
                itemsIndexed(getPulseDetailItems()) { index, item ->
                    when (index) {
                        0 -> PulseHeadCell(pulseModel)
                        1 -> PulseDataCell(pulseModel)
                        2 -> PulseFeatureCell(pulseModel)
                        3 -> PulseDiagnoseCell(pulseModel)
                        4 -> PulseDetailCell(
                            icon = R.drawable.con_ysjy,
                            title = "饮食建议",
                            content = pulseModel.dietAdvice.ifEmpty { pulseModel.suggestions["diet"] ?: "" }
                        )
                        5 -> PulseDetailCell(
                            icon = R.drawable.con_shzx,
                            title = "生活作息",
                            content = pulseModel.lifestyleAdvice.ifEmpty { pulseModel.suggestions["lifestyle"] ?: "" }
                        )
                        6 -> PulseDetailCell(
                            icon = R.drawable.con_yddl,
                            title = "运动锻炼",
                            content = pulseModel.exerciseAdvice.ifEmpty { pulseModel.suggestions["exercise"] ?: "" }
                        )
                        7 -> PulseDetailCell(
                            icon = R.drawable.con_xqtj,
                            title = "心情调节",
                            content = pulseModel.emotionalAdvice.ifEmpty { pulseModel.suggestions["emotion"] ?: "" }
                        )
                    }
                }
            }
        }

        // 删除确认弹窗
        if (showDeleteDialog) {
            AlertDialog(
                onDismissRequest = { showDeleteDialog = false },
                title = {
                    Text(
                        text = "确认删除",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF333333)
                    )
                },
                text = {
                    Text(
                        text = "确定要删除这条脉象记录吗？",
                        fontSize = 16.sp,
                        color = Color(0xFF666666)
                    )
                },
                confirmButton = {
                    TextButton(
                        onClick = {
                            // 执行删除操作
                            recordId?.let { id ->
                                homeDataManager.deleteRecords(listOf(id))
                            }
                            showDeleteDialog = false
                            onDeleteClick()
                        }
                    ) {
                        Text(
                            text = "删除",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFFAE8772)
                        )
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = { showDeleteDialog = false }
                    ) {
                        Text(
                            text = "取消",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF666666)
                        )
                    }
                },
                containerColor = Color(0xFFFFFFFF),
                shape = RoundedCornerShape(12.dp)
            )
        }
    }
}

/**
 * 自定义顶部导航栏 - 对应iOS的导航栏设置
 */
@Composable
fun PulseDetailTopBar(
    onBackClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(44.dp)
            .padding(horizontal = 0.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 返回按钮
        IconButton(
            onClick = onBackClick,
            modifier = Modifier.size(60.dp, 44.dp)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = Color(0xFF333333)
            )
        }
        
        // 标题
        Text(
            text = "脉象详情",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333),
            textAlign = TextAlign.Center,
            modifier = Modifier.weight(1f)
        )
        
        // 删除按钮
        TextButton(
            onClick = onDeleteClick,
            modifier = Modifier.size(75.dp, 44.dp)
        ) {
            Text(
                text = "删除",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF7E5F31)
            )
        }
    }
}

/**
 * 脉象头部Cell - 对应iOS的PulseHeadTableViewCell
 * 包含轮播图、脉象名称、测量时间
 */
@Composable
fun PulseHeadCell(pulseModel: PulseResultModel) {
    val pulseImages = getPulseImages(pulseModel.pulseType)
    val pagerState = rememberPagerState { pulseImages.size }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 轮播图区域 - 对应iOS的JXBanner
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.size(width = 270.dp, height = 126.dp)
        ) { page ->
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(pulseImages[page])
                    .build(),
                contentDescription = "脉象图片 ${page + 1}",
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(8.dp)),
                contentScale = ContentScale.Crop
            )
        }

        // 圆点指示器 - 对应iOS的JXPageControlScale
        if (pulseImages.size > 1) {
            Row(
                modifier = Modifier
                    .padding(top = 5.dp)
                    .height(15.dp),
                horizontalArrangement = Arrangement.spacedBy(5.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                repeat(pulseImages.size) { index ->
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .clip(CircleShape)
                            .background(
                                if (pagerState.currentPage == index)
                                    Color(0x99978163)
                                else
                                    Color(0x66C7BDB1)
                            )
                    )
                }
            }
        }

        // "本次脉象"标签
        Text(
            text = "本次脉象",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFFA1968A),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 10.dp)
        )

        // 脉象名称
        Text(
            text = pulseModel.pulseType,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 5.dp)
        )

        // 测量时间
        Text(
            text = pulseModel.measurementTime.ifEmpty { "刚刚" },
            fontSize = 12.sp,
            color = Color(0xFFA1968A),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 5.dp, bottom = 10.dp)
        )
    }
}

/**
 * 获取脉象详情页面项目列表
 */
private fun getPulseDetailItems(): List<String> {
    return listOf(
        "head", "data", "feature", "diagnose",
        "diet", "lifestyle", "exercise", "emotion"
    )
}

/**
 * 获取脉象对应的图片资源列表
 */
private fun getPulseImages(pulseType: String): List<Int> {
    return when (pulseType) {
        "平脉" -> listOf(R.drawable.pulse_pm1)
        "洪数脉" -> listOf(R.drawable.pulse_hsm1, R.drawable.pulse_hsm2)
        "滑数脉" -> listOf(R.drawable.pulse_huasm1, R.drawable.pulse_huasm2)
        "涩数脉" -> listOf(R.drawable.pulse_ssm1, R.drawable.pulse_ssm2)
        "弦滑脉" -> listOf(R.drawable.pulse_xhm1, R.drawable.pulse_xhm2)
        "弦结脉" -> listOf(R.drawable.pulse_xjm1, R.drawable.pulse_xjm2)
        "细弱脉" -> listOf(R.drawable.pulse_xrm1, R.drawable.pulse_xrm2)
        "细数脉" -> listOf(R.drawable.pulse_xsm1, R.drawable.pulse_xsm2)
        "弦数脉" -> listOf(R.drawable.pulse_xuansm1, R.drawable.pulse_xuansm2)
        "濡迟脉" -> listOf(R.drawable.pulse_rcm1, R.drawable.pulse_rcm2)
        else -> listOf(R.drawable.pulse_pm1) // 默认平脉
    }
}

/**
 * 脉象数据Cell - 对应iOS的PulseDataTableViewCell
 * 显示频率、脉力、脉势三个数据
 */
@Composable
fun PulseDataCell(pulseModel: PulseResultModel) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp, vertical = 9.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFDFA)),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFFFDFA)),
            shape = RoundedCornerShape(20.dp),
            border = androidx.compose.foundation.BorderStroke(1.dp, Color(0xFFF0E9E1))
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(140.dp)
            ) {
                // 标题栏
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(58.dp)
                        .padding(horizontal = 15.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.pulse_detail_bm),
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )

                    Text(
                        text = "脉象数据",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF333333),
                        modifier = Modifier.padding(start = 8.dp)
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    Image(
                        painter = painterResource(id = R.drawable.home_info),
                        contentDescription = "信息",
                        modifier = Modifier.size(16.dp)
                    )
                }

                // 数据展示区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    // 频率 - 使用iOS格式数据
                    PulseDataItem(
                        title = "频率",
                        value = pulseModel.rateDescription.ifEmpty { getRateDescription(pulseModel.heartRate) },
                        modifier = Modifier.weight(1f)
                    )

                    // 分割线
                    Box(
                        modifier = Modifier
                            .width(1.dp)
                            .height(48.dp)
                            .background(Color(0xFFF0E9DA))
                    )

                    // 脉力 - 使用iOS格式数据
                    PulseDataItem(
                        title = "脉力",
                        value = pulseModel.strengthDescription.ifEmpty { getStrengthDescription(pulseModel) },
                        modifier = Modifier.weight(1f)
                    )

                    // 分割线
                    Box(
                        modifier = Modifier
                            .width(1.dp)
                            .height(48.dp)
                            .background(Color(0xFFF0E9DA))
                    )

                    // 脉势 - 使用iOS格式数据
                    PulseDataItem(
                        title = "脉势",
                        value = pulseModel.trendDescription.ifEmpty { getTrendDescription(pulseModel) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * 脉象数据项组件
 */
@Composable
fun PulseDataItem(
    title: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFFA1968A),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 8.dp)
        )

        Text(
            text = value,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 10.dp)
        )
    }
}

/**
 * 脉象特征Cell - 对应iOS的PulseFeatureTableViewCell
 */
@Composable
fun PulseFeatureCell(pulseModel: PulseResultModel) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp, vertical = 9.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFDFA)),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFFFDFA)),
            shape = RoundedCornerShape(20.dp),
            border = androidx.compose.foundation.BorderStroke(1.dp, Color(0xFFF0E9E1))
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(15.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧脉象名称背景
                Box(
                    modifier = Modifier
                        .size(width = 50.dp, height = 80.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.pulse_detail_bg),
                        contentDescription = null,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Fit
                    )

                    // 垂直显示脉象名称
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        pulseModel.pulseType.forEach { char ->
                            Text(
                                text = char.toString(),
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.White,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }

                // 右侧特征描述 - 使用iOS格式数据
                Text(
                    text = pulseModel.pulseFeatureText.ifEmpty {
                        pulseModel.pulseFeatures.joinToString("\n")
                    },
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333),
                    lineHeight = 23.sp,
                    modifier = Modifier
                        .padding(start = 12.dp)
                        .weight(1f)
                )
            }
        }
    }
}

/**
 * 证候诊断Cell - 对应iOS的PulseDiagnoseTableViewCell
 */
@Composable
fun PulseDiagnoseCell(pulseModel: PulseResultModel) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp, vertical = 9.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFDFA)),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFFFDFA)),
            shape = RoundedCornerShape(20.dp),
            border = androidx.compose.foundation.BorderStroke(1.dp, Color(0xFFF0E9E1))
        ) {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 标题栏
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(58.dp)
                        .padding(horizontal = 15.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.pulse_detail_bgt),
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )

                    Text(
                        text = "证候诊断",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF333333),
                        modifier = Modifier.padding(start = 5.dp)
                    )
                }

                // 诊断内容列表 - 使用iOS格式数据
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 15.dp, vertical = 10.dp)
                ) {
                    if (pulseModel.diagnosisSections.isNotEmpty()) {
                        // 使用iOS格式的分段诊断数据
                        pulseModel.diagnosisSections.forEach { section ->
                            DiagnoseItem(
                                title = section["title"] ?: "",
                                content = section["content"] ?: ""
                            )
                        }
                    } else {
                        // 兜底显示原有诊断数据
                        DiagnoseItem(
                            title = "气血",
                            content = pulseModel.diagnosis
                        )
                    }
                }
            }
        }
    }
}

/**
 * 诊断项组件
 */
@Composable
fun DiagnoseItem(
    title: String,
    content: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(72.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333),
            textAlign = TextAlign.Center,
            modifier = Modifier.width(40.dp)
        )

        Text(
            text = content,
            fontSize = 14.sp,
            color = Color(0xFF333333),
            lineHeight = 19.sp,
            modifier = Modifier
                .padding(start = 44.dp)
                .weight(1f)
        )
    }
}

/**
 * 建议详情Cell - 对应iOS的PulseDetailTableViewCell
 * 用于显示饮食、生活、运动、心情建议
 */
@Composable
fun PulseDetailCell(
    icon: Int,
    title: String,
    content: String
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp, vertical = 9.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFDFA)),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFFFDFA)),
            shape = RoundedCornerShape(20.dp),
            border = androidx.compose.foundation.BorderStroke(1.dp, Color(0xFFF0E9E1))
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(15.dp)
            ) {
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = icon),
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )

                    Text(
                        text = title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF333333),
                        modifier = Modifier.padding(start = 5.dp)
                    )
                }

                // 内容描述
                Text(
                    text = content.ifEmpty { "暂无建议" },
                    fontSize = 14.sp,
                    color = Color(0xFF333333),
                    lineHeight = 19.sp,
                    modifier = Modifier.padding(top = 15.dp)
                )
            }
        }
    }
}

// 辅助函数

/**
 * 获取脉率描述
 */
private fun getRateDescription(heartRate: Int): String {
    return when {
        heartRate < 60 -> "迟脉"
        heartRate > 120 -> "疾脉"
        heartRate > 100 -> "数脉"
        else -> "平脉"
    }
}

/**
 * 获取脉力描述
 */
private fun getStrengthDescription(pulseModel: PulseResultModel): String {
    val hrvResult = pulseModel.hrvAnalysis
    return if (hrvResult != null) {
        when {
            hrvResult.timeDomain.sdNN < 20 -> "弱脉"
            hrvResult.timeDomain.sdNN > 50 -> "强脉"
            else -> "正常"
        }
    } else {
        "正常"
    }
}

/**
 * 获取脉势描述
 */
private fun getTrendDescription(pulseModel: PulseResultModel): String {
    val hrvResult = pulseModel.hrvAnalysis
    return if (hrvResult != null) {
        when {
            hrvResult.timeDomain.rmssd < 15 -> "涩脉"
            hrvResult.timeDomain.rmssd > 35 -> "滑脉"
            else -> "缓脉"
        }
    } else {
        "缓脉"
    }
}


