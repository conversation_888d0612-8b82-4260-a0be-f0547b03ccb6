package cn.flyok.bamai.ui.theme

import androidx.compose.foundation.IndicationNodeFactory
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.InteractionSource
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalRippleConfiguration
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.drawscope.ContentDrawScope
import androidx.compose.ui.node.DelegatableNode
import androidx.compose.ui.node.DrawModifierNode

/**
 * 全局禁用涟漪效果的解决方案
 * 
 * 根据Material 3文档，提供两种方案：
 * 1. 使用RippleConfiguration禁用涟漪
 * 2. 使用自定义NoRippleIndication
 */

/**
 * 方案1：使用RippleConfiguration全局禁用涟漪效果
 * 这是推荐的Material 3方式
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NoRippleTheme(content: @Composable () -> Unit) {
    CompositionLocalProvider(
        LocalRippleConfiguration provides null, // 设置为null禁用涟漪
        content = content
    )
}

/**
 * 方案2：自定义无涟漪效果的Indication
 * 用于需要完全控制的场景
 */
object NoRippleIndication : IndicationNodeFactory {
    override fun create(interactionSource: InteractionSource): DelegatableNode {
        return NoRippleIndicationNode()
    }

    override fun hashCode(): Int = -1

    override fun equals(other: Any?) = other === this
}

/**
 * 无涟漪效果的IndicationNode实现
 */
private class NoRippleIndicationNode : Modifier.Node(), DrawModifierNode {
    override fun ContentDrawScope.draw() {
        // 直接绘制内容，不添加任何涟漪效果
        drawContent()
    }
}

/**
 * 扩展函数：为Modifier添加无涟漪点击效果
 */
fun Modifier.clickableNoRipple(
    onClick: () -> Unit
): Modifier = this.clickable(
    onClick = onClick,
    indication = NoRippleIndication,
    interactionSource = MutableInteractionSource()
)

/**
 * 把脉App专用的无涟漪主题包装器
 * 在BamaiTheme基础上禁用涟漪效果
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BamaiNoRippleTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    BamaiTheme(darkTheme = darkTheme) {
        CompositionLocalProvider(
            LocalRippleConfiguration provides null,
            content = content
        )
    }
}
