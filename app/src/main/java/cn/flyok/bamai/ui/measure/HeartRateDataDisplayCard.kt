package cn.flyok.bamai.ui.measure

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max
import kotlin.math.min

/**
 * 心率原始数据展示卡片
 * 用于展示心率数组和时间戳数组的详细数据
 */
@Composable
fun HeartRateDataDisplayCard(
    model: PulseResultModel,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题
            Text(
                text = "心率原始数据",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF333333),
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 数据统计信息
            HeartRateDataStats(model)

            Spacer(modifier = Modifier.height(16.dp))

            // 心率趋势图
            if (model.heartRateData.isNotEmpty()) {
                HeartRateTrendChart(model.heartRateData)
                Spacer(modifier = Modifier.height(16.dp))
            }

            // 数据列表
            if (model.heartRateData.isNotEmpty() && model.timestamps.isNotEmpty()) {
                HeartRateDataList(
                    heartRateData = model.heartRateData,
                    timestamps = model.timestamps
                )
            } else {
                // 调试信息
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "暂无原始数据",
                        color = Color.Gray,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "调试信息:",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF666666)
                    )
                    Text(
                        text = "心率数据数组大小: ${model.heartRateData.size}",
                        fontSize = 10.sp,
                        color = Color(0xFF999999)
                    )
                    Text(
                        text = "时间戳数组大小: ${model.timestamps.size}",
                        fontSize = 10.sp,
                        color = Color(0xFF999999)
                    )
                    Text(
                        text = "测量持续时间: ${model.measurementDuration}s",
                        fontSize = 10.sp,
                        color = Color(0xFF999999)
                    )
                    Text(
                        text = "显示心率: ${model.heartRate} BPM",
                        fontSize = 10.sp,
                        color = Color(0xFF999999)
                    )
                }
            }
        }
    }
}

/**
 * 心率数据统计信息
 */
@Composable
private fun HeartRateDataStats(model: PulseResultModel) {
    val heartRateData = model.heartRateData
    
    if (heartRateData.isNotEmpty()) {
        val minHeartRate = heartRateData.minOrNull() ?: 0.0
        val maxHeartRate = heartRateData.maxOrNull() ?: 0.0
        val avgHeartRate = heartRateData.average()
        val dataCount = heartRateData.size
        
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(8.dp))
                .background(Color(0xFFF5F5F5))
                .padding(12.dp)
        ) {
            Text(
                text = "数据统计",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF666666),
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatItem("数据点数", "$dataCount 个")
                StatItem("测量时长", "${String.format("%.1f", model.measurementDuration)} 秒")
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatItem("最小心率", "${minHeartRate.toInt()} BPM")
                StatItem("最大心率", "${maxHeartRate.toInt()} BPM")
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatItem("平均心率", "${avgHeartRate.toInt()} BPM")
                StatItem("心率范围", "${(maxHeartRate - minHeartRate).toInt()} BPM")
            }
        }
    }
}

/**
 * 统计项目组件
 */
@Composable
private fun StatItem(label: String, value: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF999999)
        )
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333)
        )
    }
}

/**
 * 心率数据列表
 */
@Composable
private fun HeartRateDataList(
    heartRateData: List<Double>,
    timestamps: List<Long>
) {
    val dateFormat = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
    
    Column {
        Text(
            text = "详细数据 (最多显示前50个数据点)",
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 表头
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp))
                .background(Color(0xFFE3F2FD))
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "序号",
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1976D2),
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )
            Text(
                text = "心率 (BPM)",
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1976D2),
                modifier = Modifier.weight(2f),
                textAlign = TextAlign.Center
            )
            Text(
                text = "时间戳",
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1976D2),
                modifier = Modifier.weight(2f),
                textAlign = TextAlign.Center
            )
        }
        
        // 数据行 - 限制显示前50个数据点
        val displayData = heartRateData.take(50)
        val displayTimestamps = timestamps.take(50)
        
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
                .clip(RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp))
                .background(Color.White)
        ) {
            itemsIndexed(displayData) { index, heartRate ->
                val timestamp = displayTimestamps.getOrNull(index) ?: 0L
                val timeString = if (timestamp > 0) {
                    dateFormat.format(Date(timestamp))
                } else {
                    "N/A"
                }
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            if (index % 2 == 0) Color.White else Color(0xFFF8F9FA)
                        )
                        .padding(12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "${index + 1}",
                        fontSize = 12.sp,
                        color = Color(0xFF666666),
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "${heartRate.toInt()}",
                        fontSize = 12.sp,
                        color = Color(0xFF333333),
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.weight(2f),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = timeString,
                        fontSize = 10.sp,
                        color = Color(0xFF666666),
                        modifier = Modifier.weight(2f),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        
        if (heartRateData.size > 50) {
            Text(
                text = "... 还有 ${heartRateData.size - 50} 个数据点未显示",
                fontSize = 12.sp,
                color = Color(0xFF999999),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp)
            )
        }
    }
}

/**
 * 心率趋势图
 */
@Composable
private fun HeartRateTrendChart(
    heartRateData: List<Double>,
    modifier: Modifier = Modifier
) {
    if (heartRateData.isEmpty()) return

    Column(modifier = modifier) {
        Text(
            text = "心率趋势图",
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(150.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(Color(0xFFF8F9FA))
                .padding(16.dp)
        ) {
            val canvasWidth = size.width
            val canvasHeight = size.height

            if (heartRateData.size < 2) return@Canvas

            // 计算数据范围
            val minValue = heartRateData.minOrNull() ?: 0.0
            val maxValue = heartRateData.maxOrNull() ?: 100.0
            val valueRange = maxValue - minValue

            // 如果数据范围太小，扩展一下以便更好地显示
            val displayMinValue = if (valueRange < 10) minValue - 5 else minValue
            val displayMaxValue = if (valueRange < 10) maxValue + 5 else maxValue
            val displayRange = displayMaxValue - displayMinValue

            // 绘制网格线
            val gridColor = Color(0xFFE0E0E0)
            val gridStroke = Stroke(width = 1.dp.toPx())

            // 水平网格线
            for (i in 0..4) {
                val y = canvasHeight * i / 4
                drawLine(
                    color = gridColor,
                    start = Offset(0f, y),
                    end = Offset(canvasWidth, y),
                    strokeWidth = gridStroke.width
                )
            }

            // 垂直网格线
            for (i in 0..4) {
                val x = canvasWidth * i / 4
                drawLine(
                    color = gridColor,
                    start = Offset(x, 0f),
                    end = Offset(x, canvasHeight),
                    strokeWidth = gridStroke.width
                )
            }

            // 绘制心率曲线
            val path = Path()
            val lineColor = Color(0xFF2196F3)
            val lineStroke = Stroke(width = 2.dp.toPx())

            heartRateData.forEachIndexed { index, value ->
                val x = canvasWidth * index / (heartRateData.size - 1)
                val y = canvasHeight * (1 - (value - displayMinValue) / displayRange).toFloat()

                if (index == 0) {
                    path.moveTo(x, y)
                } else {
                    path.lineTo(x, y)
                }
            }

            drawPath(
                path = path,
                color = lineColor,
                style = lineStroke
            )

            // 绘制数据点
            heartRateData.forEachIndexed { index, value ->
                val x = canvasWidth * index / (heartRateData.size - 1)
                val y = canvasHeight * (1 - (value - displayMinValue) / displayRange).toFloat()

                drawCircle(
                    color = lineColor,
                    radius = 3.dp.toPx(),
                    center = Offset(x, y)
                )
            }
        }

        // 图表说明
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "开始",
                fontSize = 10.sp,
                color = Color(0xFF999999)
            )
            Text(
                text = "${heartRateData.size} 个数据点",
                fontSize = 10.sp,
                color = Color(0xFF999999)
            )
            Text(
                text = "结束",
                fontSize = 10.sp,
                color = Color(0xFF999999)
            )
        }
    }
}
