package cn.flyok.bamai.ui.constitution

/**
 * 体质数据类 - 对应iOS端的ConModel
 */
data class ConstitutionData(
    // 雷达图百分比数据
    val radarData: List<Double> = listOf(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
    
    // 主体质
    val mainName: String = "",
    val mainPercent: String = "",
    
    // 兼夹体质
    val minorName: String = "",
    val minorPercent: String = "",
    
    // 体质概括
    val summary: String = "",
    
    // 体质特点
    val traits: List<String> = emptyList(),
    
    // 入睡时间
    val sleepTime: String = "",
    
    // 起床时间
    val wakeupTime: String = "",
    
    // 菜单
    val recipes: List<Recipe> = emptyList(),
    
    // 食材推荐
    val recommendedFoods: List<String> = emptyList(),
    
    // 今日养动
    val todayExercise: List<String> = emptyList(),
    
    // 原则
    val principles: List<String> = emptyList(),
    
    // 切记
    val precautions: List<String> = emptyList(),
    
    // 经穴调养
    val acupointTherapy: String = "",
    
    // 易感疾病
    val diseaseRisks: List<String> = emptyList(),
    
    // 什么是
    val definition: String = "",
    
    // 形成原因
    val causes: String = "",
    
    // 是否为会员
    val isVip: Boolean = false,
    
    // 是否有足够的测量数据（3次以上）
    val hasEnoughData: Boolean = false
) {
    companion object {
        fun mock(): ConstitutionData {
            return ConstitutionData(
                radarData = listOf(70.0, 90.0, 80.0, 40.0, 35.0, 35.0, 35.0, 40.0),
                mainName = "气虚体质",
                mainPercent = "90%",
                minorName = "兼阴虚质",
                minorPercent = "40%",
                summary = "您的体质以气虚为主，兼有阴虚倾向。气虚体质的人通常表现为精神不振、容易疲劳、说话声音低微、容易出汗等特点。建议您在日常生活中注意补气养阴，适当进行温和的运动。",
                traits = listOf("容易疲劳", "精神不振", "说话声音低", "容易出汗", "面色偏白"),
                sleepTime = "22:30",
                wakeupTime = "06:30",
                recipes = listOf(
                    Recipe("黄芪炖鸡汤", "补气养血，适合气虚体质"),
                    Recipe("山药粥", "健脾益气，易于消化"),
                    Recipe("红枣银耳汤", "滋阴润燥，补气养血")
                ),
                recommendedFoods = listOf("黄芪", "人参", "山药", "红枣", "桂圆", "莲子"),
                todayExercise = listOf("太极拳", "八段锦", "散步"),
                principles = listOf("动作缓慢", "循序渐进", "避免剧烈运动"),
                precautions = listOf("避免过度劳累", "不宜大汗淋漓", "注意保暖"),
                acupointTherapy = "足三里、气海、关元等穴位",
                diseaseRisks = listOf("感冒", "消化不良", "慢性疲劳综合征"),
                definition = "气虚体质是指人体脏腑功能失调，气的化生不足，气虚不能推动血液运行，从而表现出的虚弱体质状态。",
                causes = "先天禀赋不足，或后天失养，久病耗气，或劳倦过度，或年老体衰等因素导致。",
                isVip = false, // 默认非会员状态用于演示
                hasEnoughData = true
            )
        }
    }
}

/**
 * 菜谱数据类
 */
data class Recipe(
    val name: String,
    val description: String
)

/**
 * 雷达图标签
 */
object ConstitutionRadarLabels {
    val labels = listOf(
        "平和质",
        "气虚质", 
        "阳虚质",
        "阴虚质",
        "痰湿质",
        "湿热质",
        "血瘀质",
        "气郁质"
    )
}

/**
 * 调养方案类型
 */
enum class NursingPlanType(val title: String) {
    TODAY_EXERCISE("今日养动"),
    PRINCIPLES("原则"),
    PRECAUTIONS("切记"),
    ACUPOINT_THERAPY("经穴调养")
}
