package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*

/**
 * 频域HRV指标计算器 - 基于NeuroKit hrv_frequency实现
 * 
 * 计算频域心率变异性指标：
 * - 各频段功率 (ULF, VLF, LF, HF, VHF)
 * - 标准化功率和比值
 * - 总功率
 */
class FrequencyHRV {
    
    companion object {
        private const val TAG = "FrequencyHRV"
        
        // 频段定义 (Hz)
        private val ULF_BAND = Pair(0.0, 0.0033)      // 超低频
        private val VLF_BAND = Pair(0.0033, 0.04)     // 极低频
        private val LF_BAND = Pair(0.04, 0.15)        // 低频
        private val HF_BAND = Pair(0.15, 0.4)         // 高频
        private val VHF_BAND = Pair(0.4, 0.5)         // 极高频
    }
    
    /**
     * 频域HRV指标数据类
     */
    data class FrequencyMetrics(
        // 各频段功率 (ms²)
        val ulf: Double,            // 超低频功率
        val vlf: Double,            // 极低频功率
        val lf: Double,             // 低频功率
        val hf: Double,             // 高频功率
        val vhf: Double,            // 极高频功率
        val totalPower: Double,     // 总功率

        // 标准化功率 (%)
        val lfNorm: Double,         // 标准化低频功率
        val hfNorm: Double,         // 标准化高频功率

        // 比值指标
        val lfhfRatio: Double,      // LF/HF比值
        val lnHF: Double,           // ln(HF)

        // 峰值频率 (Hz)
        val peakLF: Double,         // LF峰值频率
        val peakHF: Double          // HF峰值频率
    ) : java.io.Serializable
    
    /**
     * 功率谱密度数据类
     */
    data class PowerSpectralDensity(
        val frequencies: List<Double>,  // 频率数组
        val powers: List<Double>        // 功率数组
    )
    
    /**
     * 计算频域HRV指标
     */
    fun calculate(
        intervals: List<Double>,
        samplingRate: Int = 4,
        method: String = "welch"
    ): FrequencyMetrics {
        
        if (intervals.size < 50) { // 需要足够的数据进行频域分析
            Log.w(TAG, "Insufficient data for frequency analysis: ${intervals.size} points")
            return createEmptyMetrics()
        }
        
        Log.d(TAG, "Calculating frequency-domain HRV for ${intervals.size} intervals")
        
        // 1. 计算功率谱密度
        val psd = calculatePSD(intervals, samplingRate, method)
        
        if (psd.frequencies.isEmpty() || psd.powers.isEmpty()) {
            Log.w(TAG, "Failed to calculate PSD")
            return createEmptyMetrics()
        }
        
        // 2. 计算各频段功率
        val ulf = calculateBandPower(psd, ULF_BAND.first, ULF_BAND.second)
        val vlf = calculateBandPower(psd, VLF_BAND.first, VLF_BAND.second)
        val lf = calculateBandPower(psd, LF_BAND.first, LF_BAND.second)
        val hf = calculateBandPower(psd, HF_BAND.first, HF_BAND.second)
        val vhf = calculateBandPower(psd, VHF_BAND.first, VHF_BAND.second)
        
        val totalPower = ulf + vlf + lf + hf + vhf
        
        // 3. 计算标准化功率
        val lfhfSum = lf + hf
        val lfNorm = if (lfhfSum > 0) (lf / lfhfSum) * 100 else 0.0
        val hfNorm = if (lfhfSum > 0) (hf / lfhfSum) * 100 else 0.0
        
        // 4. 计算比值指标
        val lfhfRatio = if (hf > 0) lf / hf else 0.0
        val lnHF = if (hf > 0) ln(hf) else 0.0
        
        // 5. 找到峰值频率
        val peakLF = findPeakFrequency(psd, LF_BAND.first, LF_BAND.second)
        val peakHF = findPeakFrequency(psd, HF_BAND.first, HF_BAND.second)
        
        return FrequencyMetrics(
            ulf = ulf,
            vlf = vlf,
            lf = lf,
            hf = hf,
            vhf = vhf,
            totalPower = totalPower,
            lfNorm = lfNorm,
            hfNorm = hfNorm,
            lfhfRatio = lfhfRatio,
            lnHF = lnHF,
            peakLF = peakLF,
            peakHF = peakHF
        )
    }
    
    /**
     * 计算功率谱密度 - Welch方法
     */
    private fun calculatePSD(
        intervals: List<Double>,
        samplingRate: Int,
        method: String
    ): PowerSpectralDensity {
        
        return when (method) {
            "welch" -> welchPSD(intervals, samplingRate)
            "periodogram" -> periodogramPSD(intervals, samplingRate)
            else -> welchPSD(intervals, samplingRate)
        }
    }
    
    /**
     * Welch方法计算PSD
     */
    private fun welchPSD(intervals: List<Double>, samplingRate: Int): PowerSpectralDensity {
        val n = intervals.size
        val windowSize = minOf(256, n / 4) // 窗口大小
        val overlap = windowSize / 2 // 50%重叠
        
        if (windowSize < 8) {
            return periodogramPSD(intervals, samplingRate) // 数据太少，使用周期图
        }
        
        val frequencies = mutableListOf<Double>()
        val powers = mutableListOf<Double>()
        
        // 频率数组
        val freqStep = samplingRate.toDouble() / windowSize
        for (i in 0 until windowSize / 2) {
            frequencies.add(i * freqStep)
        }
        
        // 初始化功率数组
        repeat(windowSize / 2) { powers.add(0.0) }
        
        var segmentCount = 0
        var start = 0
        
        // 分段处理
        while (start + windowSize <= n) {
            val segment = intervals.subList(start, start + windowSize)
            val segmentPSD = computeSegmentPSD(segment, samplingRate)
            
            // 累加功率
            for (i in segmentPSD.indices) {
                if (i < powers.size) {
                    powers[i] += segmentPSD[i]
                }
            }
            
            segmentCount++
            start += (windowSize - overlap)
        }
        
        // 平均化
        if (segmentCount > 0) {
            for (i in powers.indices) {
                powers[i] /= segmentCount
            }
        }
        
        return PowerSpectralDensity(frequencies, powers)
    }
    
    /**
     * 周期图方法计算PSD
     */
    private fun periodogramPSD(intervals: List<Double>, samplingRate: Int): PowerSpectralDensity {
        val result = computeSegmentPSDWithFreqs(intervals, samplingRate)
        return PowerSpectralDensity(
            frequencies = result.first,
            powers = result.second
        )
    }
    
    /**
     * 计算单个段的PSD
     */
    private fun computeSegmentPSD(
        segment: List<Double>,
        samplingRate: Int
    ): List<Double> {
        val n = segment.size
        val mean = segment.average()
        val detrended = segment.map { it - mean }

        // 应用汉宁窗
        val windowed = applyHanningWindow(detrended)

        // FFT计算 (简化实现)
        val fftResult = simpleFFT(windowed)

        // 计算功率谱
        val powers = fftResult.map { it.pow(2) / (samplingRate * n) }

        return powers.take(n/2)
    }

    /**
     * 计算单个段的PSD（带频率返回）
     */
    private fun computeSegmentPSDWithFreqs(
        segment: List<Double>,
        samplingRate: Int
    ): Pair<List<Double>, List<Double>> {
        val n = segment.size
        val mean = segment.average()
        val detrended = segment.map { it - mean }

        // 应用汉宁窗
        val windowed = applyHanningWindow(detrended)

        // FFT计算 (简化实现)
        val fftResult = simpleFFT(windowed)

        // 计算功率谱
        val powers = fftResult.map { it.pow(2) / (samplingRate * n) }
        val frequencies = (0 until n/2).map { it * samplingRate.toDouble() / n }

        return Pair(frequencies, powers.take(n/2))
    }
    
    /**
     * 应用汉宁窗
     */
    private fun applyHanningWindow(data: List<Double>): List<Double> {
        val n = data.size
        return data.mapIndexed { i, value ->
            val window = 0.5 * (1 - cos(2 * PI * i / (n - 1)))
            value * window
        }
    }
    
    /**
     * 简化的FFT实现 (仅用于演示，实际应用建议使用专业FFT库)
     */
    private fun simpleFFT(data: List<Double>): List<Double> {
        val n = data.size
        val result = mutableListOf<Double>()
        
        // 简化的DFT实现
        for (k in 0 until n/2) {
            var real = 0.0
            var imag = 0.0
            
            for (i in data.indices) {
                val angle = -2 * PI * k * i / n
                real += data[i] * cos(angle)
                imag += data[i] * sin(angle)
            }
            
            result.add(sqrt(real * real + imag * imag))
        }
        
        return result
    }
    
    /**
     * 计算频段功率
     */
    private fun calculateBandPower(
        psd: PowerSpectralDensity,
        lowFreq: Double,
        highFreq: Double
    ): Double {
        var power = 0.0
        val freqStep = if (psd.frequencies.size > 1) {
            psd.frequencies[1] - psd.frequencies[0]
        } else {
            1.0
        }
        
        for (i in psd.frequencies.indices) {
            val freq = psd.frequencies[i]
            if (freq >= lowFreq && freq <= highFreq) {
                power += psd.powers[i] * freqStep
            }
        }
        
        return power
    }
    
    /**
     * 找到频段内的峰值频率
     */
    private fun findPeakFrequency(
        psd: PowerSpectralDensity,
        lowFreq: Double,
        highFreq: Double
    ): Double {
        var maxPower = 0.0
        var peakFreq = 0.0
        
        for (i in psd.frequencies.indices) {
            val freq = psd.frequencies[i]
            if (freq >= lowFreq && freq <= highFreq) {
                if (psd.powers[i] > maxPower) {
                    maxPower = psd.powers[i]
                    peakFreq = freq
                }
            }
        }
        
        return peakFreq
    }
    
    /**
     * 创建空的指标对象
     */
    private fun createEmptyMetrics(): FrequencyMetrics {
        return FrequencyMetrics(
            ulf = 0.0, vlf = 0.0, lf = 0.0, hf = 0.0, vhf = 0.0,
            totalPower = 0.0, lfNorm = 0.0, hfNorm = 0.0,
            lfhfRatio = 0.0, lnHF = 0.0, peakLF = 0.0, peakHF = 0.0
        )
    }
}
