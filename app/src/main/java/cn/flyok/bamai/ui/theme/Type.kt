package cn.flyok.bamai.ui.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R

// 定义iOS端使用的字体系列
val SystFontFamily = FontFamily(
    Font(R.font.syst_regular, FontWeight.Normal),
    Font(R.font.syst_medium, FontWeight.Medium),
    Font(R.font.syst_bold, FontWeight.Bold)
)

// iOS端字体规范 - 完全复刻
val Typography = Typography(
    // 主标题 - 30pt粗体 (iOS: KFontBoldSize(30))
    headlineLarge = TextStyle(
        fontFamily = SystFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 30.sp,
        lineHeight = 36.sp,
        letterSpacing = 0.sp
    ),
    // 导航标题 - 24pt粗体 (iOS: KFontBoldSize(24))
    headlineMedium = TextStyle(
        fontFamily = SystFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 24.sp,
        lineHeight = 30.sp,
        letterSpacing = 0.sp
    ),
    // 时辰标题 - 18pt粗体 (iOS: KFontBoldSize(18))
    titleLarge = TextStyle(
        fontFamily = SystFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 18.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.sp
    ),
    // 卡片标题 - 16pt粗体 (iOS: KFontBoldSize(16))
    titleMedium = TextStyle(
        fontFamily = SystFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 16.sp,
        lineHeight = 22.sp,
        letterSpacing = 0.sp
    ),
    // 按钮文字 - 14pt粗体 (iOS: KFontBoldSize(14))
    titleSmall = TextStyle(
        fontFamily = SystFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.sp
    ),
    // 正文 - 14pt中等 (iOS: KFontMediumSize(14))
    bodyLarge = TextStyle(
        fontFamily = SystFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.sp
    ),
    // 副文本 - 12pt普通 (iOS: KFontOfSize(12))
    bodyMedium = TextStyle(
        fontFamily = SystFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 12.sp,
        lineHeight = 18.sp,
        letterSpacing = 0.sp
    ),
    // 小文本 - 10pt普通 (iOS: KFontOfSize(10))
    bodySmall = TextStyle(
        fontFamily = SystFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 10.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.sp
    ),
    // 标签文字 - 14pt粗体
    labelLarge = TextStyle(
        fontFamily = SystFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.sp
    )
)