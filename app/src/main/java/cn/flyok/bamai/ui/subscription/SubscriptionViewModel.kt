package cn.flyok.bamai.ui.subscription

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import cn.flyok.bamai.data.*
import cn.flyok.bamai.payment.WechatPayManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 订阅页面ViewModel
 */
class SubscriptionViewModel(
    private val subscriptionManager: SubscriptionManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SubscriptionUiState())
    val uiState: StateFlow<SubscriptionUiState> = _uiState.asStateFlow()
    
    /**
     * 加载订阅套餐列表
     */
    fun loadSubscriptionPlans() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            when (val result = subscriptionManager.getSubscriptionPlans()) {
                is SubscriptionResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        plans = result.data,
                        selectedPlan = result.data.firstOrNull { it.isPopular } ?: result.data.firstOrNull()
                    )
                }
                is SubscriptionResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                }
                is SubscriptionResult.Exception -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "加载失败，请检查网络连接"
                    )
                }
            }
        }
    }
    
    /**
     * 选择订阅套餐
     */
    fun selectPlan(plan: SubscriptionPlan) {
        _uiState.value = _uiState.value.copy(selectedPlan = plan)
    }
    
    /**
     * 选择支付方式
     */
    fun selectPaymentMethod(method: PaymentMethod) {
        _uiState.value = _uiState.value.copy(selectedPaymentMethod = method)
    }
    
    /**
     * 开始订阅流程
     */
    fun startSubscription(context: Context) {
        val currentState = _uiState.value
        val selectedPlan = currentState.selectedPlan ?: return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isProcessingPayment = true)
            
            try {
                // 1. 创建订单
                when (val orderResult = subscriptionManager.createOrder(
                    planId = selectedPlan.id,
                    paymentMethod = currentState.selectedPaymentMethod
                )) {
                    is SubscriptionResult.Success -> {
                        val order = orderResult.data
                        
                        // 2. 根据支付方式处理支付
                        when (currentState.selectedPaymentMethod) {
                            PaymentMethod.WECHAT -> {
                                handleWechatPay(context, order.orderId)
                            }
                            PaymentMethod.ALIPAY -> {
                                // TODO: 处理支付宝支付
                                _uiState.value = _uiState.value.copy(
                                    isProcessingPayment = false,
                                    paymentResult = PaymentResult.Failed("暂不支持支付宝支付")
                                )
                            }
                            PaymentMethod.APPLE_PAY -> {
                                // TODO: 处理Apple Pay
                                _uiState.value = _uiState.value.copy(
                                    isProcessingPayment = false,
                                    paymentResult = PaymentResult.Failed("暂不支持Apple Pay")
                                )
                            }
                        }
                    }
                    is SubscriptionResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isProcessingPayment = false,
                            paymentResult = PaymentResult.Failed(orderResult.message)
                        )
                    }
                    is SubscriptionResult.Exception -> {
                        _uiState.value = _uiState.value.copy(
                            isProcessingPayment = false,
                            paymentResult = PaymentResult.Failed("创建订单失败，请重试")
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isProcessingPayment = false,
                    paymentResult = PaymentResult.Failed("订阅失败: ${e.message}")
                )
            }
        }
    }
    
    /**
     * 处理微信支付
     */
    private suspend fun handleWechatPay(context: Context, orderId: String) {
        try {
            // 1. 获取微信支付参数
            when (val payParamsResult = subscriptionManager.getWechatPayParams(orderId)) {
                is SubscriptionResult.Success -> {
                    val payParams = payParamsResult.data
                    
                    // 2. 调起微信支付
                    val wechatPayManager = WechatPayManager.getInstance(context)
                    if (!wechatPayManager.isWechatPayAvailable()) {
                        _uiState.value = _uiState.value.copy(
                            isProcessingPayment = false,
                            paymentResult = PaymentResult.Failed("微信支付不可用，请安装微信客户端")
                        )
                        return
                    }
                    
                    val paymentResult = wechatPayManager.startWechatPay(payParams)
                    
                    // 3. 处理支付结果
                    when (paymentResult) {
                        is PaymentResult.Success -> {
                            // 支付成功，更新会员状态
                            when (subscriptionManager.handlePaymentSuccess(orderId)) {
                                is SubscriptionResult.Success -> {
                                    _uiState.value = _uiState.value.copy(
                                        isProcessingPayment = false,
                                        paymentResult = PaymentResult.Success
                                    )
                                }
                                else -> {
                                    _uiState.value = _uiState.value.copy(
                                        isProcessingPayment = false,
                                        paymentResult = PaymentResult.Failed("支付成功但状态更新失败，请联系客服")
                                    )
                                }
                            }
                        }
                        else -> {
                            _uiState.value = _uiState.value.copy(
                                isProcessingPayment = false,
                                paymentResult = paymentResult
                            )
                        }
                    }
                }
                is SubscriptionResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isProcessingPayment = false,
                        paymentResult = PaymentResult.Failed(payParamsResult.message)
                    )
                }
                is SubscriptionResult.Exception -> {
                    _uiState.value = _uiState.value.copy(
                        isProcessingPayment = false,
                        paymentResult = PaymentResult.Failed("获取支付参数失败，请重试")
                    )
                }
            }
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                isProcessingPayment = false,
                paymentResult = PaymentResult.Failed("微信支付失败: ${e.message}")
            )
        }
    }
    
    /**
     * 清除支付结果
     */
    fun clearPaymentResult() {
        _uiState.value = _uiState.value.copy(paymentResult = null)
    }
}

/**
 * 订阅页面UI状态
 */
data class SubscriptionUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val plans: List<SubscriptionPlan> = emptyList(),
    val selectedPlan: SubscriptionPlan? = null,
    val selectedPaymentMethod: PaymentMethod = PaymentMethod.WECHAT,
    val isProcessingPayment: Boolean = false,
    val paymentResult: PaymentResult? = null
)

/**
 * ViewModel工厂
 */
class SubscriptionViewModelFactory(
    private val subscriptionManager: SubscriptionManager
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(SubscriptionViewModel::class.java)) {
            return SubscriptionViewModel(subscriptionManager) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
