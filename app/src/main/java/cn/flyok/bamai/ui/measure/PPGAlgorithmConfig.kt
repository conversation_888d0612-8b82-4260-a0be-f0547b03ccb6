package cn.flyok.bamai.ui.measure

/**
 * PPG算法配置类
 * 集中管理所有算法参数，便于调优和配置
 */
object PPGAlgorithmConfig {
    
    /**
     * 算法模式配置
     */
    object AlgorithmMode {
        // 是否启用增强算法（默认启用）
        const val USE_ENHANCED_ALGORITHM = true
        
        // 是否启用调试日志
        const val ENABLE_DEBUG_LOGGING = true
        
        // 是否启用性能监控
        const val ENABLE_PERFORMANCE_MONITORING = false
    }
    
    /**
     * 信号处理配置
     */
    object SignalProcessing {
        // 采样率 (fps)
        const val SAMPLE_RATE = 30.0
        
        // 带通滤波器频率范围
        const val LOW_CUTOFF_FREQ = 0.5   // Hz (30 BPM)
        const val HIGH_CUTOFF_FREQ = 4.0  // Hz (240 BPM)
        
        // 滤波器窗口大小
        const val MOVING_AVERAGE_WINDOW = 5
        const val MEDIAN_FILTER_WINDOW = 3
        
        // 信号缓冲区大小
        const val SIGNAL_BUFFER_SIZE = 300 // 10秒 * 30fps
    }
    
    /**
     * 峰值检测配置
     */
    object PeakDetection {
        // 峰值间隔限制（样本数）
        const val MIN_PEAK_DISTANCE = 15  // 对应2Hz最大频率
        const val MAX_PEAK_DISTANCE = 60  // 对应0.5Hz最小频率
        
        // 自适应阈值参数
        const val THRESHOLD_ADAPTATION_RATE = 0.1
        const val INITIAL_THRESHOLD_RATIO = 0.3
        
        // 导数分析窗口
        const val DERIVATIVE_WINDOW = 3
        
        // 信号历史缓冲区大小
        const val SIGNAL_HISTORY_SIZE = 100
    }
    
    /**
     * 信号质量评估配置
     */
    object QualityAssessment {
        // 质量评估窗口大小
        const val QUALITY_WINDOW_SIZE = 60      // 2秒窗口
        const val STABILITY_WINDOW_SIZE = 90    // 3秒窗口
        const val MOTION_DETECTION_WINDOW = 30  // 1秒窗口
        
        // 质量阈值
        const val MIN_SNR_THRESHOLD = 3.0
        const val MAX_NOISE_RATIO = 0.3
        const val MAX_VARIANCE_THRESHOLD = 0.1
        const val MOTION_THRESHOLD = 0.2
    }
    
    /**
     * 心率检测配置
     */
    object HeartRateDetection {
        // 测量窗口时间（秒）
        const val MEASUREMENT_WINDOW_SECONDS = 10
        
        // 有效心率范围
        const val MIN_HEART_RATE = 30
        const val MAX_HEART_RATE = 180
        
        // 心率历史记录大小
        const val HEART_RATE_HISTORY_SIZE = 10
        
        // 早期完成条件
        const val EARLY_COMPLETION_MIN_SAMPLES = 3
        val EARLY_COMPLETION_QUALITY_THRESHOLD = SignalQualityAssessment.SignalQuality.EXCELLENT
    }
    
    /**
     * 用户界面配置
     */
    object UserInterface {
        // 进度更新频率（毫秒）
        const val PROGRESS_UPDATE_INTERVAL = 100
        
        // 质量反馈更新频率（毫秒）
        const val QUALITY_FEEDBACK_INTERVAL = 500
        
        // 状态变化动画时长（毫秒）
        const val STATE_CHANGE_ANIMATION_DURATION = 200
    }
    
    /**
     * 性能优化配置
     */
    object Performance {
        // 线程池大小
        const val THREAD_POOL_SIZE = 2
        
        // 内存缓存大小
        const val MEMORY_CACHE_SIZE = 1024 * 1024 // 1MB
        
        // 垃圾回收触发阈值
        const val GC_TRIGGER_THRESHOLD = 0.8
    }
    
    /**
     * 调试和测试配置
     */
    object Debug {
        // 是否保存原始信号数据
        const val SAVE_RAW_SIGNAL_DATA = false
        
        // 是否保存处理后的信号数据
        const val SAVE_PROCESSED_SIGNAL_DATA = false
        
        // 是否启用算法性能分析
        const val ENABLE_ALGORITHM_PROFILING = false
        
        // 测试模式（使用模拟数据）
        const val TEST_MODE = false
    }
    
    /**
     * 获取算法配置摘要
     */
    fun getConfigSummary(): String {
        return """
            PPG Algorithm Configuration:
            - Enhanced Algorithm: ${AlgorithmMode.USE_ENHANCED_ALGORITHM}
            - Sample Rate: ${SignalProcessing.SAMPLE_RATE} fps
            - Filter Range: ${SignalProcessing.LOW_CUTOFF_FREQ}-${SignalProcessing.HIGH_CUTOFF_FREQ} Hz
            - Measurement Window: ${HeartRateDetection.MEASUREMENT_WINDOW_SECONDS}s
            - Heart Rate Range: ${HeartRateDetection.MIN_HEART_RATE}-${HeartRateDetection.MAX_HEART_RATE} BPM
            - Debug Logging: ${AlgorithmMode.ENABLE_DEBUG_LOGGING}
        """.trimIndent()
    }
    
    /**
     * 验证配置参数的有效性
     */
    fun validateConfiguration(): List<String> {
        val issues = mutableListOf<String>()
        
        // 验证采样率
        if (SignalProcessing.SAMPLE_RATE <= 0) {
            issues.add("Sample rate must be positive")
        }
        
        // 验证滤波器频率
        if (SignalProcessing.LOW_CUTOFF_FREQ >= SignalProcessing.HIGH_CUTOFF_FREQ) {
            issues.add("Low cutoff frequency must be less than high cutoff frequency")
        }
        
        // 验证心率范围
        if (HeartRateDetection.MIN_HEART_RATE >= HeartRateDetection.MAX_HEART_RATE) {
            issues.add("Minimum heart rate must be less than maximum heart rate")
        }
        
        // 验证窗口大小
        if (SignalProcessing.MOVING_AVERAGE_WINDOW <= 0 || 
            SignalProcessing.MEDIAN_FILTER_WINDOW <= 0) {
            issues.add("Filter window sizes must be positive")
        }
        
        // 验证峰值检测参数
        if (PeakDetection.MIN_PEAK_DISTANCE >= PeakDetection.MAX_PEAK_DISTANCE) {
            issues.add("Minimum peak distance must be less than maximum peak distance")
        }
        
        return issues
    }
    
    /**
     * 重置为默认配置
     */
    fun resetToDefaults() {
        // 这里可以添加重置逻辑，如果需要运行时配置修改的话
        // 当前实现使用编译时常量，所以不需要运行时重置
    }
    
    /**
     * 获取推荐的配置用于不同场景
     */
    object RecommendedConfigs {
        
        /**
         * 高精度模式配置
         */
        fun getHighAccuracyConfig(): Map<String, Any> {
            return mapOf(
                "measurementWindow" to 15,
                "qualityThreshold" to SignalQualityAssessment.SignalQuality.GOOD,
                "minSamples" to 5
            )
        }
        
        /**
         * 快速测量模式配置
         */
        fun getFastMeasurementConfig(): Map<String, Any> {
            return mapOf(
                "measurementWindow" to 8,
                "qualityThreshold" to SignalQualityAssessment.SignalQuality.FAIR,
                "minSamples" to 2
            )
        }
        
        /**
         * 低功耗模式配置
         */
        fun getLowPowerConfig(): Map<String, Any> {
            return mapOf(
                "sampleRate" to 20.0,
                "bufferSize" to 200,
                "updateInterval" to 200
            )
        }
    }
}
