package cn.flyok.bamai.ui.measure

import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.abs

/**
 * 心率测量结果数据类
 */
data class HeartRateMeasurementResult(
    val heartRateData: List<Double>, // 心率数据数组
    val timestamps: List<Long>, // 对应的时间戳数组
    val measurementDuration: Double // 测量持续时间（秒）
)

/**
 * PPG心跳检测器 - 增强版
 * 移植自android-heart-rate-monitor的HeartRateMonitor.java
 * 集成了高级信号处理、峰值检测和质量评估算法
 *
 * 主要增强功能：
 * - 高级信号滤波（带通滤波、移动平均、中值滤波）
 * - 自适应峰值检测算法
 * - 实时信号质量评估
 * - 运动伪影检测和处理
 */
class HeartRateDetector {

    companion object {
        private const val TAG = "HeartRateDetector"

        // 测量窗口时间（秒）- 对应iOS的35秒总时长
        private const val MEASUREMENT_WINDOW_SECONDS = 35

        // 预热时间（秒）- 给算法足够时间稳定
        private const val WARMUP_SECONDS = 3

        // 有效心率范围
        private const val MIN_HEART_RATE = 30
        private const val MAX_HEART_RATE = 180

        // 只使用增强算法
    }
    
    /**
     * 心跳检测状态枚举
     */
    enum class HeartbeatType {
        GREEN, RED
    }
    
    // 处理状态标志
    private val processing = AtomicBoolean(false)

    // POS算法模块 - 基于IEEE 2017论文
    private val signalProcessor = PPGSignalProcessor()
    private val peakDetector = POSPeakDetector()
    private val qualityAssessment = SignalQualityAssessment()

    // HRV分析模块
    private val hrvAnalyzer = HRVAnalyzer()

    // 脉象分析模块
    private val pulseWaveformAnalyzer = PulseWaveformAnalyzer()



    // 心跳检测相关
    private var currentType = HeartbeatType.GREEN
    private var beats = 0.0
    private var startTime = 0L

    // 增强算法状态
    private var lastHeartRateUpdate = 0L
    private val heartRateHistory = mutableListOf<Int>()

    // 心率平滑
    private val heartRateSmoothing = mutableListOf<Int>()
    private var lastSmoothedHeartRate = 0
    
    // 回调接口
    private var onMeasurementComplete: ((HeartRateMeasurementResult) -> Unit)? = null
    private var onMeasurementProgress: ((Float) -> Unit)? = null
    private var onHeartbeatStateChanged: ((HeartbeatType) -> Unit)? = null
    private var onSignalQualityChanged: ((SignalQualityAssessment.SignalQuality) -> Unit)? = null

    // 测量数据收集
    private val heartRateDataList = mutableListOf<Double>()
    private val timestampsList = mutableListOf<Long>()
    private var measurementStartTime = 0L



    // 波形数据管理器（可选）
    private var waveformDataManager: WaveformDataManager? = null
    
    /**
     * 设置测量完成回调
     */
    fun setOnMeasurementComplete(callback: (HeartRateMeasurementResult) -> Unit) {
        onMeasurementComplete = callback
    }
    
    /**
     * 设置测量进度回调
     */
    fun setOnMeasurementProgress(callback: (Float) -> Unit) {
        onMeasurementProgress = callback
    }
    
    /**
     * 设置心跳状态变化回调
     */
    fun setOnHeartbeatStateChanged(callback: (HeartbeatType) -> Unit) {
        onHeartbeatStateChanged = callback
    }

    /**
     * 设置信号质量变化回调
     */
    fun setOnSignalQualityChanged(callback: (SignalQualityAssessment.SignalQuality) -> Unit) {
        onSignalQualityChanged = callback
    }

    /**
     * 设置波形数据管理器
     */
    fun setWaveformDataManager(manager: WaveformDataManager) {
        waveformDataManager = manager
    }
    
    /**
     * 开始心率检测
     */
    fun startDetection() {
        reset()
        startTime = System.currentTimeMillis()
        measurementStartTime = startTime
        lastHeartRateUpdate = startTime

        // 通知波形数据管理器开始测量
        waveformDataManager?.startMeasurement()

        Log.d(TAG, "Heart rate detection started with enhanced algorithm")
    }
    
    /**
     * 停止心率检测
     */
    fun stopDetection() {
        processing.set(false)
        Log.d(TAG, "Heart rate detection stopped")
    }
    
    /**
     * 重置检测器状态
     */
    private fun reset() {
        // 重置POS算法模块
        signalProcessor.reset()
        peakDetector.reset()
        qualityAssessment.reset()

        // 重置波形数据管理器
        waveformDataManager?.clearAllData()

        // 重置状态
        currentType = HeartbeatType.GREEN
        processing.set(false)

        // 重置增强算法状态
        heartRateHistory.clear()
        heartRateSmoothing.clear()
        lastSmoothedHeartRate = 0

        // 重置测量数据收集
        heartRateDataList.clear()
        timestampsList.clear()
        measurementStartTime = 0L

        Log.d(TAG, "HeartRateDetector reset")
    }
    
    /**
     * 处理新的图像帧
     * 这是心跳检测的核心方法，支持原始算法和增强算法
     *
     * @param imageData YUV420SP格式的图像数据
     * @param width 图像宽度
     * @param height 图像高度
     */
    fun processFrame(imageData: ByteArray, width: Int, height: Int) {
        // 使用原子操作确保同时只有一个线程在处理
        if (!processing.compareAndSet(false, true)) {
            return
        }

        try {
            // 提取RGB像素数据用于POS算法
            val rgbPixels = ImageProcessing.decodeYUV420SPtoRGB(imageData, width, height)

            // 检查图像质量
            if (rgbPixels.isEmpty()) {
                return
            }

            // 使用POS算法处理完整的RGB数据
            processFrameWithPixels(rgbPixels, width, height)

        } finally {
            processing.set(false)
        }
    }

    /**
     * 使用POS算法处理帧 - 基于IEEE 2017论文
     */
    private fun processFrameEnhanced(rawValue: Int) {
        // 注意：这个方法现在需要像素数组，但为了兼容性暂时保留
        // 实际应该使用 processFrameWithPixels 方法
        Log.w(TAG, "processFrameEnhanced with single rawValue is deprecated, use processFrameWithPixels instead")
    }

    /**
     * 使用POS算法处理帧（完整像素数据）
     */
    fun processFrameWithPixels(pixels: IntArray, width: Int, height: Int) {
        // 1. POS信号处理 - 需要完整的RGB数据
        val processedSignal = signalProcessor.processSample(pixels, width, height)

        // 2. 向波形数据管理器添加数据
        waveformDataManager?.let { manager ->
            // 计算红色通道平均值用于兼容性
            val redSum = pixels.sumOf { (it shr 16) and 0xFF }
            val redAverage = redSum.toDouble() / pixels.size

            manager.addRawSample(redAverage)
            manager.addProcessedSample(processedSignal)
        }

        // 3. 信号质量评估
        val signalQuality = qualityAssessment.assessQuality(processedSignal, processedSignal)
        val isSignalGood = signalProcessor.isSignalQualityGood()
        onSignalQualityChanged?.invoke(signalQuality)
        waveformDataManager?.updateSignalQuality(signalQuality)

        // 4. POS峰值检测
        val valleyDetected = peakDetector.processSample(processedSignal)

        // 5. 处理检测到的峰值 - 恢复正确的HRV数据收集逻辑
        if (valleyDetected) {
            // 更新心跳状态
            currentType = HeartbeatType.RED
            onHeartbeatStateChanged?.invoke(currentType)

            // **关键修复：添加峰值到波形数据管理器**
            waveformDataManager?.let { manager ->
                val currentIndex = manager.processedSignalData.value.size - 1
                manager.addPeak(currentIndex.coerceAtLeast(0), 1.0)
                Log.d(TAG, "Peak added at index $currentIndex")
            }

            // 获取当前心率估计
            val currentHeartRate = peakDetector.getCurrentHeartRate()

            Log.d(TAG, "Valley detected - Heart rate: $currentHeartRate, Signal good: $isSignalGood, Peak added to waveform manager")

            // 基于真实峰值检测收集心率数据（HRV分析的正确方法）
            if (currentHeartRate != null && currentHeartRate in MIN_HEART_RATE..MAX_HEART_RATE) {
                val currentTime = System.currentTimeMillis()
                val totalTimeInSecs = (currentTime - startTime) / 1000.0

                // 预热时间后开始收集数据
                if (totalTimeInSecs >= WARMUP_SECONDS) {
                    heartRateDataList.add(currentHeartRate.toDouble())
                    timestampsList.add(currentTime)
                    Log.d(TAG, "HRV data collected - HR: $currentHeartRate, Time: ${totalTimeInSecs}s, Total points: ${heartRateDataList.size}")
                }

                updateHeartRateHistory(currentHeartRate)
                waveformDataManager?.updateHeartRate(currentHeartRate)
            }
        } else {
            currentType = HeartbeatType.GREEN
            onHeartbeatStateChanged?.invoke(currentType)
        }

        // 6. 更新测量进度和检查完成条件
        val currentTime = System.currentTimeMillis()
        val totalTimeInSecs = (currentTime - startTime) / 1000.0
        val progress = (totalTimeInSecs / MEASUREMENT_WINDOW_SECONDS).toFloat().coerceAtMost(1.0f)
        onMeasurementProgress?.invoke(progress)

        // 检查是否完成测量
        if (totalTimeInSecs >= MEASUREMENT_WINDOW_SECONDS) {
            calculateEnhancedHeartRate()
        }
    }
    



    
    /**
     * 更新心率历史（增强算法）- 添加平滑处理
     */
    private fun updateHeartRateHistory(heartRate: Int) {
        // 放宽异常值过滤：允许更大的心率变化
        val isValidHeartRate = if (lastSmoothedHeartRate > 0) {
            val change = abs(heartRate - lastSmoothedHeartRate)
            change <= 35 // 心率变化不超过35 BPM（放宽限制）
        } else {
            true
        }

        if (isValidHeartRate) {
            heartRateSmoothing.add(heartRate)

            // 保持最近5个有效心率值
            if (heartRateSmoothing.size > 5) {
                heartRateSmoothing.removeAt(0)
            }

            // 计算平滑后的心率（加权平均，最新的权重更大）
            val weights = doubleArrayOf(0.1, 0.15, 0.2, 0.25, 0.3)
            var weightedSum = 0.0
            var totalWeight = 0.0

            for (i in heartRateSmoothing.indices) {
                val weight = weights.getOrElse(i) { 1.0 / heartRateSmoothing.size }
                weightedSum += heartRateSmoothing[i] * weight
                totalWeight += weight
            }

            lastSmoothedHeartRate = (weightedSum / totalWeight).toInt()
            heartRateHistory.add(lastSmoothedHeartRate)
        } else {
            // 如果是异常值，使用上一个平滑值
            if (lastSmoothedHeartRate > 0) {
                heartRateHistory.add(lastSmoothedHeartRate)
            }
        }

        // 保持最近的心率记录
        if (heartRateHistory.size > 10) {
            heartRateHistory.removeAt(0)
        }

        lastHeartRateUpdate = System.currentTimeMillis()
    }

    /**
     * 计算增强算法的最终心率
     */
    private fun calculateEnhancedHeartRate() {
        // 停止检测，防止继续处理
        processing.set(false)

        val currentTime = System.currentTimeMillis()
        val measurementDuration = (currentTime - measurementStartTime) / 1000.0

        // 创建测量结果（只包含数据数组，不计算最终心率）
        val result = HeartRateMeasurementResult(
            heartRateData = heartRateDataList.toList(),
            timestamps = timestampsList.toList(),
            measurementDuration = measurementDuration
        )

        Log.d(TAG, "Heart rate measurement completed: ${heartRateDataList.size} data points, ${measurementDuration}s duration")
        Log.d(TAG, "Heart rate data range: ${heartRateDataList.minOrNull()} - ${heartRateDataList.maxOrNull()} BPM")

        onMeasurementComplete?.invoke(result)
    }


    /**
     * 获取当前心跳状态
     */
    fun getCurrentHeartbeatType(): HeartbeatType {
        return currentType
    }
    
    /**
     * 检查是否正在处理
     */
    fun isProcessing(): Boolean {
        return processing.get()
    }
    
    /**
     * 获取当前测量统计信息
     */
    fun getMeasurementStats(): MeasurementStats {
        val currentTime = System.currentTimeMillis()
        val elapsedTime = (currentTime - startTime) / 1000.0
        val progress = (elapsedTime / MEASUREMENT_WINDOW_SECONDS).toFloat().coerceAtMost(1.0f)

        return MeasurementStats(
            elapsedTime = elapsedTime,
            progress = progress,
            beatsDetected = heartRateHistory.size,
            currentType = currentType,
            isEnhancedMode = true,
            signalQuality = qualityAssessment.getCurrentQuality()
        )
    }

    /**
     * 获取信号质量报告（仅增强模式）
     */
    fun getSignalQualityReport(): SignalQualityAssessment.QualityReport? {
        return qualityAssessment.getDetailedQualityReport()
    }

    /**
     * 获取峰值检测统计信息（POS算法）
     */
    fun getPeakDetectionStats(): POSPeakDetector.DetectionStatistics? {
        return peakDetector.getDetectionStatistics()
    }

    /**
     * 获取HRV分析结果
     */
    fun getHRVAnalysis(): HRVAnalyzer.HRVAnalysisResult? {
        val peaks = peakDetector.getAllPeaks()
        val currentTime = System.currentTimeMillis()
        val measurementDuration = (currentTime - startTime) / 1000.0

        // 检查是否有足够的数据进行HRV分析
        return if (peaks.size >= 30 && measurementDuration >= 60.0) {
            try {
                val result = hrvAnalyzer.analyzeHRV(peaks)
                Log.d(TAG, "HRV analysis completed: valid=${result.isValid}, score=${result.assessment.overallScore}")
                result
            } catch (e: Exception) {
                Log.e(TAG, "Error during HRV analysis", e)
                null
            }
        } else {
            Log.d(TAG, "Insufficient data for HRV analysis: peaks=${peaks.size}, duration=${measurementDuration}s")
            null
        }
    }

    /**
     * 获取处理后的PPG信号数据（用于HRV分析）
     */
    fun getProcessedSignalData(): List<Double> {
        return signalProcessor.getProcessedSignalBuffer()
    }

    /**
     * 获取所有检测到的峰值（用于HRV分析）
     */
    fun getAllDetectedPeaks(): List<POSPeakDetector.SignalMeasurement> {
        return peakDetector.getAllPeaks()
    }

    /**
     * 获取完整的波形数据（用于脉象分析）
     */
    fun getWaveformData(): WaveformDataManager.WaveformExportData? {
        return waveformDataManager?.exportWaveformData()
    }

    /**
     * 获取脉象分析结果
     */
    fun getPulseAnalysis(): PulseWaveformAnalyzer.PulseAnalysisResult? {
        val waveformData = getWaveformData()
        if (waveformData == null) {
            Log.d(TAG, "No waveform data available for pulse analysis")
            return null
        }

        Log.d(TAG, "Starting pulse analysis with ${waveformData.processedData.size} data points and ${waveformData.peaks.size} peaks")

        return try {
            val result = pulseWaveformAnalyzer.analyzePulse(waveformData)
            Log.d(TAG, "Pulse analysis result: ${result?.overallType?.description}, confidence: ${result?.confidence}")
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error during pulse analysis", e)
            null
        }
    }

    /**
     * 获取心跳周期数据
     */
    fun getHeartbeatCycles(): List<WaveformDataManager.HeartbeatCycle> {
        return waveformDataManager?.getHeartbeatCycles() ?: emptyList()
    }

    /**
     * 获取平均心跳波形
     */
    fun getAverageHeartbeatWaveform(): WaveformDataManager.AverageWaveform? {
        return waveformDataManager?.getAverageHeartbeatWaveform()
    }

    /**
     * 获取信号处理统计信息（仅增强模式）
     */
    fun getSignalProcessingStats(): PPGSignalProcessor.SignalStatistics? {
        return signalProcessor.getSignalStatistics()
    }
    
    /**
     * 测量统计信息数据类
     */
    data class MeasurementStats(
        val elapsedTime: Double,
        val progress: Float,
        val beatsDetected: Int,
        val currentType: HeartbeatType,
        val isEnhancedMode: Boolean = false,
        val signalQuality: SignalQualityAssessment.SignalQuality? = null
    )
}
