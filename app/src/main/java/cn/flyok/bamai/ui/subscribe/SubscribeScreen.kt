package cn.flyok.bamai.ui.subscribe

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.common.ClickEffects.lightScale
import cn.flyok.bamai.ui.common.ClickEffects.strongScale
import cn.flyok.bamai.ui.theme.*

/**
 * 订阅页面主界面 - 完全复刻iOS端SubscribeController
 * 
 * 结构说明：
 * - 导航栏：返回按钮 + 装饰背景
 * - 滚动内容：标题、订阅选项、说明图片
 * - 底部固定：立即解锁按钮 + 链接 + 说明文字
 */
@Composable
fun SubscribeScreen(
    onBackClick: () -> Unit,
    onSubscribeClick: (SubscriptionType) -> Unit,
    onRestorePurchaseClick: () -> Unit,
    onUserAgreementClick: () -> Unit,
    onPrivacyPolicyClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidth = configuration.screenWidthDp.dp
    
    // 当前选择的订阅类型
    var selectedSubscription by remember { mutableStateOf(SubscriptionType.MONTHLY) }
    
    Box(modifier = modifier.fillMaxSize()) {
        // 主内容区域
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .background(BamaiViewBackColor),
            contentPadding = PaddingValues(
                top = 100.dp, // 为固定导航栏预留空间
                bottom = 160.dp // 为底部按钮预留空间
            )
        ) {
            
            // 标题区域
            item {
                SubscribeTitleSection(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 30.dp, vertical = 22.dp)
                )
            }
            
            // 订阅选项区域
            item {
                SubscriptionOptionsSection(
                    selectedSubscription = selectedSubscription,
                    onSubscriptionSelected = { selectedSubscription = it },
                    screenWidth = screenWidth,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp, vertical = 38.dp)
                )
            }
            
            // 订阅说明图片
            item {
                SubscribeDescriptionImage(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 20.dp, bottom = 40.dp)
                )
            }
        }

        // 固定导航栏
        SubscribeNavigationBar(
            onBackClick = onBackClick,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .fillMaxWidth()
        )

        // 底部固定区域
        SubscribeBottomSection(
            selectedSubscription = selectedSubscription,
            onSubscribeClick = onSubscribeClick,
            onRestorePurchaseClick = onRestorePurchaseClick,
            onUserAgreementClick = onUserAgreementClick,
            onPrivacyPolicyClick = onPrivacyPolicyClick,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(BamaiViewBackColor)
        )
    }
}

/**
 * 导航栏组件 - 复刻iOS端导航栏设计
 */
@Composable
private fun SubscribeNavigationBar(
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .height(100.dp)
            .background(BamaiViewBackColor)
    ) {
        // 返回按钮 - 调整位置和大小，添加点击动效
        Image(
            painter = painterResource(id = R.drawable.home_off),
            contentDescription = "返回",
            modifier = Modifier
                .size(30.dp, 22.dp) // 缩小按钮尺寸
                .align(Alignment.TopStart)
                .offset(x = 15.dp, y = 50.dp) // 调整位置
                .strongScale(onClick = onBackClick)
        )
        
        // 装饰背景
        Image(
            painter = painterResource(id = R.drawable.home_zzbg),
            contentDescription = null,
            modifier = Modifier
                .size(131.dp, 153.dp)
                .align(Alignment.TopEnd)
                .offset(y = 15.dp)
        )
    }
}

/**
 * 标题区域组件
 */
@Composable
private fun SubscribeTitleSection(
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 主标题 - 部分文字红色高亮
        val titleText = buildAnnotatedString {
            append("认准正版")
            withStyle(style = SpanStyle(color = Color(0xFFCD3C3C))) {
                append("「把脉」")
            }
            append("APP")
        }
        
        Text(
            text = titleText,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333),
            modifier = Modifier.height(30.dp)
        )
        
        Spacer(modifier = Modifier.height(5.dp))
        
        // 副标题
        Text(
            text = "专属定制您的健康方案，让健康更进一步",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFFA1968A),
            modifier = Modifier.height(20.dp)
        )
    }
}

/**
 * 订阅选项区域组件
 */
@Composable
private fun SubscriptionOptionsSection(
    selectedSubscription: SubscriptionType,
    onSubscriptionSelected: (SubscriptionType) -> Unit,
    screenWidth: androidx.compose.ui.unit.Dp,
    modifier: Modifier = Modifier
) {
    val scale = screenWidth.value / 375f
    val cardWidth = (105 * scale).dp
    val cardHeight = (120 * scale).dp

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        SubscriptionType.values().forEach { subscription ->
            SubscriptionCard(
                subscription = subscription,
                isSelected = selectedSubscription == subscription,
                onSelected = { onSubscriptionSelected(subscription) },
                cardWidth = cardWidth,
                cardHeight = cardHeight
            )
        }
    }
}

/**
 * 单个订阅卡片组件
 */
@Composable
private fun SubscriptionCard(
    subscription: SubscriptionType,
    isSelected: Boolean,
    onSelected: () -> Unit,
    cardWidth: androidx.compose.ui.unit.Dp,
    cardHeight: androidx.compose.ui.unit.Dp,
    modifier: Modifier = Modifier
) {
    // 外层容器负责动效
    Box(
        modifier = modifier
            .lightScale(onClick = onSelected)
    ) {
        // 卡片主体 - 包含背景和所有视觉元素
        Box(
            modifier = Modifier
                .size(cardWidth, cardHeight)
                .clip(RoundedCornerShape(20.dp))
                .background(
                    if (isSelected) Color(0x1AA1968A) else Color.White
                )
                .border(
                    width = if (isSelected) 1.2.dp else 0.dp,
                    color = if (isSelected) Color(0xFFAE8772) else Color.Transparent,
                    shape = RoundedCornerShape(20.dp)
                )
        ) {
        // 背景图片
        Image(
            painter = painterResource(
                id = if (isSelected) R.drawable.home_price_bg_hight
                     else R.drawable.home_price_bg_none
            ),
            contentDescription = null,
            modifier = Modifier
                .size(105.dp, 45.dp)
                .align(Alignment.BottomEnd)
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 会员类型
            Text(
                text = subscription.title,
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFFAE8772),
                textAlign = TextAlign.Center,
                modifier = Modifier.height(20.dp)
            )

            Spacer(modifier = Modifier.height(5.dp))

            // 价格 - 符号小字体
            val priceText = buildAnnotatedString {
                withStyle(style = SpanStyle(fontSize = 12.sp)) {
                    append("¥")
                }
                append(subscription.price.substring(1))
            }

            Text(
                text = priceText,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF333333),
                textAlign = TextAlign.Center,
                modifier = Modifier.height(30.dp)
            )

            Spacer(modifier = Modifier.weight(1f))

            // 原价格 - 删除线
            Text(
                text = subscription.originalPrice,
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFFA1968A),
                textAlign = TextAlign.Center,
                textDecoration = TextDecoration.LineThrough,
                modifier = Modifier.height(20.dp)
            )
        }
        }
    }
}

/**
 * 订阅说明图片组件
 */
@Composable
private fun SubscribeDescriptionImage(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = R.drawable.home_orderdes),
            contentDescription = "订阅说明",
            modifier = Modifier.size(300.dp, 256.dp)
        )
    }
}

/**
 * 底部固定区域组件
 */
@Composable
private fun SubscribeBottomSection(
    selectedSubscription: SubscriptionType,
    onSubscribeClick: (SubscriptionType) -> Unit,
    onRestorePurchaseClick: () -> Unit,
    onUserAgreementClick: () -> Unit,
    onPrivacyPolicyClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(horizontal = 20.dp, vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 底部链接
        SubscribeBottomLinks(
            onRestorePurchaseClick = onRestorePurchaseClick,
            onUserAgreementClick = onUserAgreementClick,
            onPrivacyPolicyClick = onPrivacyPolicyClick,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 立即解锁按钮 - 双层设计
        SubscribeButton(
            selectedSubscription = selectedSubscription,
            onSubscribeClick = onSubscribeClick
        )
    }
}

/**
 * 底部链接组件
 */
@Composable
private fun SubscribeBottomLinks(
    onRestorePurchaseClick: () -> Unit,
    onUserAgreementClick: () -> Unit,
    onPrivacyPolicyClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "用户协议",
            fontSize = 12.sp,
            color = Color(0xFF73665A),
            modifier = Modifier.strongScale(onClick = onUserAgreementClick)
        )

        Text(
            text = "   |   ",
            fontSize = 12.sp,
            color = Color(0xFFA1968A)
        )

        Text(
            text = "隐私协议",
            fontSize = 12.sp,
            color = Color(0xFF73665A),
            modifier = Modifier.strongScale(onClick = onPrivacyPolicyClick)
        )
    }
}

/**
 * 立即解锁按钮组件 - 双层设计复刻iOS端
 */
@Composable
private fun SubscribeButton(
    selectedSubscription: SubscriptionType,
    onSubscribeClick: (SubscriptionType) -> Unit,
    modifier: Modifier = Modifier
) {
    // 外层容器负责动效
    Box(
        modifier = modifier
            .lightScale(onClick = { onSubscribeClick(selectedSubscription) }),
        contentAlignment = Alignment.Center
    ) {
        // 按钮主体 - 包含背景和所有视觉元素
        Box(
            modifier = Modifier
                .size(280.dp, 48.dp)
                .clip(RoundedCornerShape(24.dp))
                .background(Color(0xFFAE8772)),
            contentAlignment = Alignment.Center
        ) {
            // 内层按钮
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(5.dp)
                    .clip(RoundedCornerShape(19.dp))
                    .background(Color(0xFFAE8772))
                    .border(
                        width = 1.dp,
                        color = Color(0xFFE5D6CE),
                        shape = RoundedCornerShape(19.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "立即解锁",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SubscribeScreenPreview() {
    BamaiNoRippleTheme {
        SubscribeScreen(
            onBackClick = {},
            onSubscribeClick = {},
            onRestorePurchaseClick = {},
            onUserAgreementClick = {},
            onPrivacyPolicyClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun SubscriptionCardPreview() {
    BamaiNoRippleTheme {
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.padding(16.dp)
        ) {
            SubscriptionCard(
                subscription = SubscriptionType.MONTHLY,
                isSelected = true,
                onSelected = {},
                cardWidth = 105.dp,
                cardHeight = 120.dp
            )
            SubscriptionCard(
                subscription = SubscriptionType.YEARLY,
                isSelected = false,
                onSelected = {},
                cardWidth = 105.dp,
                cardHeight = 120.dp
            )
            SubscriptionCard(
                subscription = SubscriptionType.PERMANENT,
                isSelected = false,
                onSelected = {},
                cardWidth = 105.dp,
                cardHeight = 120.dp
            )
        }
    }
}
