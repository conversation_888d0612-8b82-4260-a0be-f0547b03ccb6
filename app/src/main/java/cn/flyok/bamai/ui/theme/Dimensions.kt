package cn.flyok.bamai.ui.theme

import androidx.compose.ui.unit.dp

/**
 * iOS端尺寸规范 - 完全复刻
 * 基于iOS端的设计规范定义Android端的尺寸系统
 */
object BamaiDimensions {
    // 圆角半径
    val cardCornerRadius = 20.dp        // 卡片圆角
    val buttonCornerRadius = 23.dp      // 按钮圆角
    val innerButtonCornerRadius = 18.dp // 内层按钮圆角
    val tagCornerRadius = 15.dp         // 标签圆角

    // 间距
    val paddingSmall = 5.dp
    val paddingMedium = 10.dp
    val paddingLarge = 15.dp
    val paddingXLarge = 20.dp

    // 卡片间距
    val cardMarginHorizontal = 20.dp
    val cardMarginVertical = 6.dp
    val cardPadding = 5.dp

    // 按钮尺寸
    val buttonWidth = 130.dp
    val buttonHeight = 46.dp
    val buttonSpacing = 12.dp

    // 图标尺寸
    val logoWidth = 200.dp
    val logoHeight = 150.dp
    val timeIconWidth = 43.dp
    val timeIconHeight = 66.dp
    val arrowIconSize = 18.dp
    val lockIconSize = 16.dp
    val tagSize = 30.dp

    // 卡片高度
    val timeCardHeight = 110.dp
    val recordItemHeight = 82.dp

    // 边框宽度
    val borderWidth = 1.dp

    // 导航栏
    val navBarHeight = 60.dp
    val statusBarOffset = 10.dp  // iOS端logo距离顶部的距离
}
