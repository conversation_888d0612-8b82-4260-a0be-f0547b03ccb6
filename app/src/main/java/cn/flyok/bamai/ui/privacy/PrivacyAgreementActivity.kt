package cn.flyok.bamai.ui.privacy

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import cn.flyok.bamai.data.LoginManager
import cn.flyok.bamai.data.userManager
import cn.flyok.bamai.network.NetworkResult
import cn.flyok.bamai.ui.theme.BamaiNoRippleTheme
import kotlinx.coroutines.launch

/**
 * 隐私政策同意页面Activity - 完全复刻iOS端AppPrivacyController
 * 
 * 功能：
 * - 首次启动显示隐私政策
 * - 用户同意后自动登录
 * - 跳转到主界面
 */
class PrivacyAgreementActivity : ComponentActivity() {
    
    companion object {
        private const val PREFS_NAME = "bamai_app_config"
        private const val KEY_FIRST_LAUNCH = "is_first_launch"
        
        /**
         * 创建启动Intent
         */
        fun createIntent(context: Context): Intent {
            return Intent(context, PrivacyAgreementActivity::class.java)
        }
        
        /**
         * 检查是否首次启动
         */
        fun isFirstLaunch(context: Context): Boolean {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            return prefs.getBoolean(KEY_FIRST_LAUNCH, true)
        }
        
        /**
         * 标记已完成首次启动
         */
        fun markFirstLaunchCompleted(context: Context) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putBoolean(KEY_FIRST_LAUNCH, false).apply()
        }
    }
    
    private lateinit var loginManager: LoginManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        loginManager = LoginManager(this)
        
        setContent {
            BamaiNoRippleTheme {
                Surface(
                    modifier = Modifier.fillMaxSize()
                ) {
                    var isLoading by remember { mutableStateOf(false) }
                    var errorMessage by remember { mutableStateOf<String?>(null) }

                    PrivacyAgreementScreen(
                        isLoading = isLoading,
                        errorMessage = errorMessage,
                        onAgreeClick = {
                            handleAgreeClick { loading, error ->
                                isLoading = loading
                                errorMessage = error
                            }
                        },
                        onPrivacyPolicyClick = {
                            // TODO: 跳转到隐私政策页面
                            handlePrivacyPolicyClick()
                        },
                        onUserAgreementClick = {
                            // TODO: 跳转到用户协议页面
                            handleUserAgreementClick()
                        }
                    )
                }
            }
        }
    }
    
    /**
     * 处理同意按钮点击
     */
    private fun handleAgreeClick(onStateChange: (Boolean, String?) -> Unit) {
        lifecycleScope.launch {
            try {
                onStateChange(true, null)
                
                // 执行设备ID自动登录
                when (val result = loginManager.performDeviceLogin()) {
                    is NetworkResult.Success -> {
                        // 登录成功，标记首次启动完成
                        markFirstLaunchCompleted(this@PrivacyAgreementActivity)
                        
                        // 跳转到主界面
                        navigateToMain()
                    }
                    is NetworkResult.Error -> {
                        onStateChange(false, "登录失败: ${result.message}")
                    }
                    is NetworkResult.Exception -> {
                        onStateChange(false, "网络异常，请检查网络连接")
                    }
                }
                
            } catch (e: Exception) {
                onStateChange(false, "发生未知错误")
                android.util.Log.e("PrivacyAgreementActivity", "同意按钮处理失败", e)
            }
        }
    }
    
    /**
     * 处理隐私政策点击
     */
    private fun handlePrivacyPolicyClick() {
        // TODO: 实现隐私政策页面跳转
        android.util.Log.d("PrivacyAgreementActivity", "点击隐私政策")
    }
    
    /**
     * 处理用户协议点击
     */
    private fun handleUserAgreementClick() {
        // TODO: 实现用户协议页面跳转
        android.util.Log.d("PrivacyAgreementActivity", "点击用户协议")
    }
    


    /**
     * 跳转到主界面
     */
    private fun navigateToMain() {
        // 启动主界面
        val intent = Intent(this, cn.flyok.bamai.MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
    
    override fun onBackPressed() {
        // 首次启动页面不允许返回
        // 用户必须同意隐私政策才能继续使用
    }
}
