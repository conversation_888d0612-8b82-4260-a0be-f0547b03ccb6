package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*

/**
 * POS峰值检测器 - 基于华盛顿大学实现优化
 * 
 * 使用简单但有效的谷值检测方法，专门配合POS算法使用
 * 参考：ubicomplab/rPPG-Data-Collection-Android
 */
class POSPeakDetector {
    
    companion object {
        private const val TAG = "POSPeakDetector"
        
        // 检测参数 - 优化以提高检测敏感性
        private const val SAMPLE_RATE = 30.0 // Android摄像头采样率
        private const val VALLEY_DETECTION_WINDOW = 7 // 减小窗口以提高敏感性
        private const val MIN_HEART_RATE = 30
        private const val MAX_HEART_RATE = 180
        
        // 信号缓冲区大小
        private const val SIGNAL_BUFFER_SIZE = 300 // 10秒
    }
    
    // 信号缓冲区
    private val signalBuffer = mutableListOf<SignalMeasurement>()
    
    // 检测到的谷值
    private val valleys = mutableListOf<Long>()
    
    // 统计信息
    private var detectedValleys = 0
    private var minimum = Double.MAX_VALUE
    private var maximum = Double.MIN_VALUE
    
    /**
     * 信号测量数据类
     */
    data class SignalMeasurement(
        val timestamp: Long,
        val value: Double
    ) : java.io.Serializable
    
    /**
     * 检测统计信息
     */
    data class DetectionStatistics(
        val totalValleys: Int,
        val currentHeartRate: Int,
        val signalQuality: Double
    )
    
    /**
     * 处理新的信号样本并检测峰值
     * 
     * @param signalValue POS算法输出的信号值
     * @return 如果检测到谷值则返回true
     */
    fun processSample(signalValue: Double): Boolean {
        val currentTime = System.currentTimeMillis()
        
        // 添加测量值
        addMeasurement(signalValue, currentTime)
        
        // 检测谷值
        return detectValley()
    }
    
    /**
     * 添加测量值到缓冲区
     */
    private fun addMeasurement(value: Double, timestamp: Long) {
        val measurement = SignalMeasurement(timestamp, value)
        signalBuffer.add(measurement)
        
        // 更新最小值和最大值
        if (value < minimum) minimum = value
        if (value > maximum) maximum = value
        
        // 保持缓冲区大小
        if (signalBuffer.size > SIGNAL_BUFFER_SIZE) {
            signalBuffer.removeAt(0)
        }
    }
    
    /**
     * 检测谷值 - 改进版本，增强稳定性
     */
    private fun detectValley(): Boolean {
        if (signalBuffer.size < VALLEY_DETECTION_WINDOW) {
            return false
        }

        // 获取最近的测量值
        val recentMeasurements = signalBuffer.takeLast(VALLEY_DETECTION_WINDOW)
        val middleIndex = VALLEY_DETECTION_WINDOW / 2
        val referenceValue = recentMeasurements[middleIndex].value

        // 更严格的谷值检测：检查是否为局部最小值
        var isLocalMinimum = true
        for (i in recentMeasurements.indices) {
            if (i != middleIndex && recentMeasurements[i].value <= referenceValue) {
                isLocalMinimum = false
                break
            }
        }

        if (!isLocalMinimum) return false

        // 检查谷值深度（降低阈值以提高敏感性）
        val leftMax = recentMeasurements.subList(0, middleIndex).maxOfOrNull { it.value } ?: referenceValue
        val rightMax = recentMeasurements.subList(middleIndex + 1, recentMeasurements.size).maxOfOrNull { it.value } ?: referenceValue
        val valleyDepth = minOf(leftMax, rightMax) - referenceValue

        // 降低谷值深度阈值
        if (valleyDepth < 0.005) return false // 从0.01降低到0.005

        // 检查与上一个谷值的时间间隔（放宽限制）
        if (valleys.isNotEmpty()) {
            val lastValleyTime = valleys.last()
            val currentTime = recentMeasurements[middleIndex].timestamp
            val timeDiff = (currentTime - lastValleyTime) / 1000.0

            // 最小间隔0.3秒（对应200 BPM）- 放宽限制
            if (timeDiff < 0.3) return false
        }

        // 检测到有效谷值
        detectedValleys++
        valleys.add(recentMeasurements[middleIndex].timestamp)

        // 保持谷值历史大小
        if (valleys.size > 15) { // 减少历史大小，保持更新
            valleys.removeAt(0)
        }

        Log.d(TAG, "Valley detected, total: $detectedValleys, depth: $valleyDepth")
        return true
    }
    
    /**
     * 获取当前心率估计 - 基于谷值检测（权威方法）
     */
    fun getCurrentHeartRate(): Int? {
        if (valleys.size < 2) return null // 至少需要2个谷值

        val recentValleys = valleys.takeLast(6) // 使用最近6个谷值
        if (recentValleys.size < 2) return null

        // 计算间隔并过滤异常值
        val intervals = mutableListOf<Double>()
        for (i in 1 until recentValleys.size) {
            val interval = (recentValleys[i] - recentValleys[i-1]) / 1000.0 // 转换为秒
            // 放宽间隔过滤：对应30-180 BPM
            if (interval >= 0.33 && interval <= 2.0) {
                intervals.add(interval)
            }
        }

        if (intervals.isEmpty()) return null

        // 简化计算：使用中位数或平均值
        val avgInterval = if (intervals.size >= 3) {
            // 有足够数据时使用中位数
            val sortedIntervals = intervals.sorted()
            sortedIntervals[sortedIntervals.size / 2]
        } else {
            // 数据较少时直接使用平均值
            intervals.average()
        }

        val heartRate = (60.0 / avgInterval).toInt()
        return heartRate.coerceIn(MIN_HEART_RATE, MAX_HEART_RATE)
    }
    
    /**
     * 获取检测统计信息
     */
    fun getDetectionStatistics(): DetectionStatistics {
        val heartRate = getCurrentHeartRate() ?: 0
        
        // 计算信号质量（基于信号范围）
        val signalRange = maximum - minimum
        val signalQuality = if (signalRange > 0) {
            minOf(1.0, signalRange / 100.0) // 简单的质量评估
        } else {
            0.0
        }
        
        return DetectionStatistics(
            totalValleys = detectedValleys,
            currentHeartRate = heartRate,
            signalQuality = signalQuality
        )
    }
    
    /**
     * 获取标准化的信号值（用于绘图）
     */
    fun getStandardizedValues(): List<Pair<Long, Float>> {
        if (signalBuffer.isEmpty() || maximum == minimum) {
            return emptyList()
        }

        return signalBuffer.map { measurement ->
            val normalizedValue = ((measurement.value - minimum) / (maximum - minimum)).toFloat()
            Pair(measurement.timestamp, normalizedValue)
        }
    }

    /**
     * 获取所有检测到的峰值（用于HRV分析）
     */
    fun getAllPeaks(): List<SignalMeasurement> {
        // 从谷值时间戳重建峰值信息
        return valleys.mapNotNull { timestamp ->
            // 在信号缓冲区中找到对应的测量值
            signalBuffer.find { abs(it.timestamp - timestamp) < 100 } // 100ms容差
        }
    }

    /**
     * 获取信号缓冲区（用于HRV分析）
     */
    fun getSignalBuffer(): List<SignalMeasurement> {
        return signalBuffer.toList()
    }
    
    /**
     * 重置检测器状态
     */
    fun reset() {
        signalBuffer.clear()
        valleys.clear()
        detectedValleys = 0
        minimum = Double.MAX_VALUE
        maximum = Double.MIN_VALUE
        Log.d(TAG, "POS peak detector reset")
    }
}
