package cn.flyok.bamai.ui.subscribe

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import cn.flyok.bamai.data.homeDataManager
import cn.flyok.bamai.data.userManager
import cn.flyok.bamai.ui.theme.BamaiNoRippleTheme

/**
 * 订阅页面Activity - 完全复刻iOS端SubscribeController
 * 
 * 功能：
 * - 1:1还原iOS端订阅页面视觉效果
 * - 三种会员套餐选择：月度/年度/永久
 * - 预留订阅接口，暂不实现实际支付
 * - 小红书推广弹窗功能
 */
class SubscribeActivity : ComponentActivity() {
    
    companion object {
        /**
         * 创建启动Intent
         */
        fun createIntent(context: Context): Intent {
            return Intent(context, SubscribeActivity::class.java)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            BamaiNoRippleTheme {
                Surface(
                    modifier = Modifier.fillMaxSize()
                ) {
                    var showXiaohongshuDialog by remember { mutableStateOf(false) }

                    SubscribeScreen(
                        onBackClick = {
                            // 复刻iOS端退出逻辑：非订阅用户显示小红书推广弹窗
                            val isSubscribed by homeDataManager.isSubscribed
                            if (!isSubscribed) {
                                showXiaohongshuDialog = true
                            } else {
                                finish()
                            }
                        },
                        onSubscribeClick = { subscriptionType ->
                            // TODO: 实现订阅逻辑
                            handleSubscription(subscriptionType) { success ->
                                if (success) {
                                    // 订阅成功后直接跳转到主页面，不再需要绑定手机号
                                    finish()
                                }
                            }
                        },
                        onRestorePurchaseClick = {
                            // TODO: 实现恢复购买逻辑
                            handleRestorePurchase()
                        },
                        onUserAgreementClick = {
                            // TODO: 跳转到用户协议页面
                            handleUserAgreement()
                        },
                        onPrivacyPolicyClick = {
                            // TODO: 跳转到隐私政策页面
                            handlePrivacyPolicy()
                        }
                    )

                    // 小红书推广弹窗
                    if (showXiaohongshuDialog) {
                        XiaohongshuPromotionDialog(
                            onDismiss = {
                                showXiaohongshuDialog = false
                                finish()
                            },
                            onFollowClick = {
                                showXiaohongshuDialog = false
                                // TODO: 跳转到小红书页面
                                handleXiaohongshuFollow()
                                finish()
                            }
                        )
                    }


                }
            }
        }
    }
    
    /**
     * 处理订阅逻辑
     */
    private fun handleSubscription(subscriptionType: SubscriptionType, onResult: (Boolean) -> Unit) {
        // TODO: 实现实际的订阅逻辑
        // 这里预留接口，后续对接支付系统
        android.util.Log.d("SubscribeActivity", "订阅类型: ${subscriptionType.name}")

        // 模拟订阅成功
        // 实际应该调用Google Play Billing API
        onResult(true)
    }
    
    /**
     * 处理恢复购买
     */
    private fun handleRestorePurchase() {
        // TODO: 实现恢复购买逻辑
        android.util.Log.d("SubscribeActivity", "恢复购买")
    }
    
    /**
     * 处理用户协议点击
     */
    private fun handleUserAgreement() {
        // TODO: 跳转到用户协议页面
        android.util.Log.d("SubscribeActivity", "用户协议")
    }
    
    /**
     * 处理隐私政策点击
     */
    private fun handlePrivacyPolicy() {
        // TODO: 跳转到隐私政策页面
        android.util.Log.d("SubscribeActivity", "隐私政策")
    }

    /**
     * 处理小红书关注点击
     */
    private fun handleXiaohongshuFollow() {
        // TODO: 跳转到小红书页面
        android.util.Log.d("SubscribeActivity", "跳转到小红书")
    }
}

/**
 * 订阅类型枚举 - 对应iOS端的价格数组
 */
enum class SubscriptionType(
    val title: String,
    val price: String,
    val originalPrice: String,
    val productId: String
) {
    MONTHLY("月度会员", "¥8.00", "¥18.00", "fly.bamai.firstMouth"),
    YEARLY("年度会员", "¥78.00", "¥198.00", "fly.bamai.firstYear"),
    PERMANENT("永久会员", "¥88.00", "¥298.00", "flyBaMaiForever")
}
