package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*

/**
 * PPG算法性能监控器
 * 监控算法执行性能、内存使用和准确性指标
 */
class PPGPerformanceMonitor {
    
    companion object {
        private const val TAG = "PPGPerformanceMonitor"
        
        // 性能监控是否启用
        private val isEnabled = PPGAlgorithmConfig.AlgorithmMode.ENABLE_PERFORMANCE_MONITORING
    }
    
    // 性能指标
    private var totalFramesProcessed = 0L
    private var totalProcessingTime = 0L
    private var maxProcessingTime = 0L
    private var minProcessingTime = Long.MAX_VALUE
    
    // 内存使用监控
    private var peakMemoryUsage = 0L
    private var averageMemoryUsage = 0L
    private var memoryMeasurements = 0
    
    // 算法准确性监控
    private val heartRateAccuracyHistory = mutableListOf<AccuracyMeasurement>()
    
    // 错误统计
    private var signalQualityErrors = 0
    private var peakDetectionErrors = 0
    private var heartRateValidationErrors = 0
    
    /**
     * 开始性能监控会话
     */
    fun startMonitoringSession() {
        if (!isEnabled) return
        
        reset()
        Log.d(TAG, "Performance monitoring session started")
    }
    
    /**
     * 结束性能监控会话并生成报告
     */
    fun endMonitoringSession(): PerformanceReport {
        if (!isEnabled) return PerformanceReport.empty()
        
        val report = generatePerformanceReport()
        Log.d(TAG, "Performance monitoring session ended")
        Log.i(TAG, report.toString())
        
        return report
    }
    
    /**
     * 监控帧处理性能
     */
    fun monitorFrameProcessing(processingTimeMs: Long) {
        if (!isEnabled) return
        
        totalFramesProcessed++
        totalProcessingTime += processingTimeMs
        
        if (processingTimeMs > maxProcessingTime) {
            maxProcessingTime = processingTimeMs
        }
        
        if (processingTimeMs < minProcessingTime) {
            minProcessingTime = processingTimeMs
        }
        
        // 检查性能警告
        if (processingTimeMs > 50) { // 超过50ms可能影响实时性
            Log.w(TAG, "Frame processing took ${processingTimeMs}ms (may affect real-time performance)")
        }
    }
    
    /**
     * 监控内存使用
     */
    fun monitorMemoryUsage() {
        if (!isEnabled) return
        
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        
        if (usedMemory > peakMemoryUsage) {
            peakMemoryUsage = usedMemory
        }
        
        averageMemoryUsage = (averageMemoryUsage * memoryMeasurements + usedMemory) / (memoryMeasurements + 1)
        memoryMeasurements++
        
        // 检查内存警告
        val memoryUsageRatio = usedMemory.toDouble() / runtime.maxMemory()
        if (memoryUsageRatio > PPGAlgorithmConfig.Performance.GC_TRIGGER_THRESHOLD) {
            Log.w(TAG, "High memory usage detected: ${(memoryUsageRatio * 100).toInt()}%")
        }
    }
    
    /**
     * 记录心率准确性测量
     */
    fun recordHeartRateAccuracy(measuredRate: Int, referenceRate: Int? = null) {
        if (!isEnabled) return
        
        val accuracy = AccuracyMeasurement(
            measuredRate = measuredRate,
            referenceRate = referenceRate,
            timestamp = System.currentTimeMillis(),
            error = referenceRate?.let { abs(measuredRate - it) }
        )
        
        heartRateAccuracyHistory.add(accuracy)
        
        // 保持历史记录大小
        if (heartRateAccuracyHistory.size > 100) {
            heartRateAccuracyHistory.removeAt(0)
        }
    }
    
    /**
     * 记录信号质量错误
     */
    fun recordSignalQualityError() {
        if (!isEnabled) return
        signalQualityErrors++
    }
    
    /**
     * 记录峰值检测错误
     */
    fun recordPeakDetectionError() {
        if (!isEnabled) return
        peakDetectionErrors++
    }
    
    /**
     * 记录心率验证错误
     */
    fun recordHeartRateValidationError() {
        if (!isEnabled) return
        heartRateValidationErrors++
    }
    
    /**
     * 获取实时性能指标
     */
    fun getRealTimeMetrics(): RealTimeMetrics {
        if (!isEnabled) return RealTimeMetrics.empty()
        
        val avgProcessingTime = if (totalFramesProcessed > 0) {
            totalProcessingTime.toDouble() / totalFramesProcessed
        } else 0.0
        
        val fps = if (totalProcessingTime > 0) {
            (totalFramesProcessed * 1000.0) / totalProcessingTime
        } else 0.0
        
        return RealTimeMetrics(
            averageProcessingTime = avgProcessingTime,
            currentFPS = fps,
            framesProcessed = totalFramesProcessed,
            memoryUsage = averageMemoryUsage
        )
    }
    
    /**
     * 重置所有监控数据
     */
    private fun reset() {
        totalFramesProcessed = 0L
        totalProcessingTime = 0L
        maxProcessingTime = 0L
        minProcessingTime = Long.MAX_VALUE
        
        peakMemoryUsage = 0L
        averageMemoryUsage = 0L
        memoryMeasurements = 0
        
        heartRateAccuracyHistory.clear()
        
        signalQualityErrors = 0
        peakDetectionErrors = 0
        heartRateValidationErrors = 0
    }
    
    /**
     * 生成性能报告
     */
    private fun generatePerformanceReport(): PerformanceReport {
        val avgProcessingTime = if (totalFramesProcessed > 0) {
            totalProcessingTime.toDouble() / totalFramesProcessed
        } else 0.0
        
        val avgFPS = if (totalProcessingTime > 0) {
            (totalFramesProcessed * 1000.0) / totalProcessingTime
        } else 0.0
        
        val accuracyStats = calculateAccuracyStatistics()
        
        return PerformanceReport(
            totalFramesProcessed = totalFramesProcessed,
            averageProcessingTime = avgProcessingTime,
            maxProcessingTime = maxProcessingTime,
            minProcessingTime = if (minProcessingTime == Long.MAX_VALUE) 0L else minProcessingTime,
            averageFPS = avgFPS,
            peakMemoryUsage = peakMemoryUsage,
            averageMemoryUsage = averageMemoryUsage,
            accuracyStatistics = accuracyStats,
            errorCounts = ErrorCounts(
                signalQualityErrors = signalQualityErrors,
                peakDetectionErrors = peakDetectionErrors,
                heartRateValidationErrors = heartRateValidationErrors
            )
        )
    }
    
    /**
     * 计算准确性统计
     */
    private fun calculateAccuracyStatistics(): AccuracyStatistics {
        if (heartRateAccuracyHistory.isEmpty()) {
            return AccuracyStatistics.empty()
        }
        
        val validMeasurements = heartRateAccuracyHistory.filter { it.error != null }
        
        if (validMeasurements.isEmpty()) {
            return AccuracyStatistics(
                totalMeasurements = heartRateAccuracyHistory.size,
                validMeasurements = 0,
                averageError = 0.0,
                maxError = 0,
                minError = 0,
                standardDeviation = 0.0
            )
        }
        
        val errors = validMeasurements.mapNotNull { it.error }
        val avgError = errors.average()
        val maxError = errors.maxOrNull() ?: 0
        val minError = errors.minOrNull() ?: 0
        
        val variance = errors.map { (it - avgError).pow(2) }.average()
        val stdDev = sqrt(variance)
        
        return AccuracyStatistics(
            totalMeasurements = heartRateAccuracyHistory.size,
            validMeasurements = validMeasurements.size,
            averageError = avgError,
            maxError = maxError,
            minError = minError,
            standardDeviation = stdDev
        )
    }
    
    /**
     * 准确性测量数据类
     */
    private data class AccuracyMeasurement(
        val measuredRate: Int,
        val referenceRate: Int?,
        val timestamp: Long,
        val error: Int?
    )
    
    /**
     * 实时性能指标数据类
     */
    data class RealTimeMetrics(
        val averageProcessingTime: Double,
        val currentFPS: Double,
        val framesProcessed: Long,
        val memoryUsage: Long
    ) {
        companion object {
            fun empty() = RealTimeMetrics(0.0, 0.0, 0L, 0L)
        }
    }
    
    /**
     * 准确性统计数据类
     */
    data class AccuracyStatistics(
        val totalMeasurements: Int,
        val validMeasurements: Int,
        val averageError: Double,
        val maxError: Int,
        val minError: Int,
        val standardDeviation: Double
    ) {
        companion object {
            fun empty() = AccuracyStatistics(0, 0, 0.0, 0, 0, 0.0)
        }
    }
    
    /**
     * 错误计数数据类
     */
    data class ErrorCounts(
        val signalQualityErrors: Int,
        val peakDetectionErrors: Int,
        val heartRateValidationErrors: Int
    ) {
        val totalErrors: Int
            get() = signalQualityErrors + peakDetectionErrors + heartRateValidationErrors
    }
    
    /**
     * 性能报告数据类
     */
    data class PerformanceReport(
        val totalFramesProcessed: Long,
        val averageProcessingTime: Double,
        val maxProcessingTime: Long,
        val minProcessingTime: Long,
        val averageFPS: Double,
        val peakMemoryUsage: Long,
        val averageMemoryUsage: Long,
        val accuracyStatistics: AccuracyStatistics,
        val errorCounts: ErrorCounts
    ) {
        companion object {
            fun empty() = PerformanceReport(
                0L, 0.0, 0L, 0L, 0.0, 0L, 0L,
                AccuracyStatistics.empty(), ErrorCounts(0, 0, 0)
            )
        }
        
        override fun toString(): String {
            return """
                PPG Algorithm Performance Report:
                ================================
                Frames Processed: $totalFramesProcessed
                Average Processing Time: ${String.format("%.2f", averageProcessingTime)}ms
                Max Processing Time: ${maxProcessingTime}ms
                Min Processing Time: ${minProcessingTime}ms
                Average FPS: ${String.format("%.1f", averageFPS)}
                Peak Memory Usage: ${peakMemoryUsage / 1024 / 1024}MB
                Average Memory Usage: ${averageMemoryUsage / 1024 / 1024}MB
                
                Accuracy Statistics:
                - Total Measurements: ${accuracyStatistics.totalMeasurements}
                - Valid Measurements: ${accuracyStatistics.validMeasurements}
                - Average Error: ${String.format("%.1f", accuracyStatistics.averageError)} BPM
                - Max Error: ${accuracyStatistics.maxError} BPM
                - Min Error: ${accuracyStatistics.minError} BPM
                - Standard Deviation: ${String.format("%.2f", accuracyStatistics.standardDeviation)}
                
                Error Counts:
                - Signal Quality Errors: ${errorCounts.signalQualityErrors}
                - Peak Detection Errors: ${errorCounts.peakDetectionErrors}
                - Heart Rate Validation Errors: ${errorCounts.heartRateValidationErrors}
                - Total Errors: ${errorCounts.totalErrors}
            """.trimIndent()
        }
    }
}
