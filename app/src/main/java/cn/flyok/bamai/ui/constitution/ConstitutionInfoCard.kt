package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*


/**
 * 体质信息卡片组件 - 完全复刻iOS端ConInfoTableViewCell
 * 
 * 特点：
 * - 白色卡片背景，带边框
 * - 显示主体质和兼夹体质及百分比
 * - 体质概括描述
 * - 体质特点标签流式布局
 * - 非会员显示锁定状态
 */
@Composable
fun ConstitutionInfoCard(
    constitutionData: ConstitutionData,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(20.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(
                            width = 1.dp,
                            color = Color(0xFFF0E9E1),
                            shape = RoundedCornerShape(20.dp)
                        )
                        .padding(1.dp)
                ) {
                    if (constitutionData.isVip) {
                        // 会员显示VIP内容（内部会检查数据量）
                        VipContent(constitutionData = constitutionData)
                    } else {
                        // 非会员显示锁定内容
                        LockedContent()
                    }
                }
            }
        }
    }
}

/**
 * 会员内容显示
 */
@Composable
private fun VipContent(
    constitutionData: ConstitutionData,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(25.dp)
    ) {
        // 根据数据量决定显示内容
        val mainName = if (constitutionData.hasEnoughData) constitutionData.mainName else "--"
        val mainPercent = if (constitutionData.hasEnoughData) constitutionData.mainPercent else "--"
        val minorName = if (constitutionData.hasEnoughData) constitutionData.minorName else "--"
        val minorPercent = if (constitutionData.hasEnoughData) constitutionData.minorPercent else "--"
        val summary = if (constitutionData.hasEnoughData) {
            constitutionData.summary
        } else {
            "您的体质报告还未生成，请先进行三次把脉测量，开启您的健康养生之旅！"
        }
        val traits = if (constitutionData.hasEnoughData) constitutionData.traits else emptyList()

        // 主体质和兼夹体质
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            // 主体质
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = mainName,
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp
                    ),
                    color = Color(0xFF333333)
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = mainPercent,
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 14.sp
                    ),
                    color = Color(0xFF333333),
                    modifier = Modifier.padding(start = 5.dp)
                )
            }

            // 兼夹体质
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = minorName,
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp
                    ),
                    color = Color(0xFF333333)
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = minorPercent,
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 14.sp
                    ),
                    color = Color(0xFF333333),
                    modifier = Modifier.padding(start = 5.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(15.dp))

        // 体质概括
        Text(
            text = summary,
            style = MaterialTheme.typography.bodyMedium.copy(
                fontSize = 14.sp,
                lineHeight = 19.sp
            ),
            color = Color(0xFF333333)
        )
        
        // 体质特点标签
        if (traits.isNotEmpty()) {
            Spacer(modifier = Modifier.height(10.dp))

            // 简化为两行显示，每行最多3个标签
            val chunkedTraits = traits.chunked(3)

            chunkedTraits.forEach { rowTraits ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(10.dp)
                ) {
                    rowTraits.forEach { trait ->
                        Card(
                            shape = RoundedCornerShape(8.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = Color(0xFFF8F4F1)
                            ),
                            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                        ) {
                            Text(
                                text = trait,
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontSize = 14.sp
                                ),
                                color = Color(0xFF333333),
                                modifier = Modifier.padding(horizontal = 15.dp, vertical = 8.dp),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }

                if (rowTraits != chunkedTraits.last()) {
                    Spacer(modifier = Modifier.height(10.dp))
                }
            }
        }
    }
}

/**
 * 锁定状态内容
 */
@Composable
private fun LockedContent(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .clickable(
                onClick = { /* TODO: 跳转订阅页面 */ },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
    ) {
        Image(
            painter = painterResource(id = R.drawable.con_infolocked),
            contentDescription = "解锁体质信息",
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.Fit
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ConstitutionInfoCardVipPreview() {
    BamaiTheme {
        ConstitutionInfoCard(
            constitutionData = ConstitutionData.mock().copy(isVip = true),
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ConstitutionInfoCardLockedPreview() {
    BamaiTheme {
        ConstitutionInfoCard(
            constitutionData = ConstitutionData.mock().copy(isVip = false),
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ConstitutionInfoCardNoDataPreview() {
    BamaiTheme {
        ConstitutionInfoCard(
            constitutionData = ConstitutionData.mock().copy(
                isVip = true,
                hasEnoughData = false,
                mainName = "",
                minorName = "",
                summary = "",
                traits = emptyList()
            ),
            modifier = Modifier.padding(20.dp)
        )
    }
}
