package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*

/**
 * 调养方案卡片组件 - 完全复刻iOS端ConNurseTableViewCell
 * 
 * 特点：
 * - 显示今日养动、原则、切记、经穴调养等内容
 * - 非会员显示锁定状态
 */
@Composable
fun NursingPlanCard(
    constitutionData: ConstitutionData,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(20.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(
                            width = 1.dp,
                            color = Color(0xFFF0E9E1),
                            shape = RoundedCornerShape(20.dp)
                        )
                        .padding(1.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(22.dp)
                    ) {
                        // 标题行
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.con_tztd),
                                contentDescription = null,
                                modifier = Modifier.size(18.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(5.dp))
                            
                            Text(
                                text = "调养方案",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp
                                ),
                                color = Color(0xFF333333)
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(20.dp))
                        
                        // 内容区域
                        if (constitutionData.isVip) {
                            VipNursingContent(constitutionData = constitutionData)
                        } else {
                            LockedNursingContent()
                        }
                    }
                }
            }
        }
    }
}

/**
 * 会员调养内容 - 复刻iOS端VipUI逻辑，包含标签页切换
 */
@Composable
private fun VipNursingContent(
    constitutionData: ConstitutionData
) {
    // 标签页状态
    var selectedTabIndex by remember { mutableStateOf(0) }

    Column {
        // 标签页切换器
        if (constitutionData.hasEnoughData) {
            NursingTabSelector(
                selectedIndex = selectedTabIndex,
                onTabSelected = { selectedTabIndex = it }
            )

            Spacer(modifier = Modifier.height(20.dp))
        }

        // 根据数据量决定显示内容
        val todayExercise = if (constitutionData.hasEnoughData) constitutionData.todayExercise else emptyList()
        val principles = if (constitutionData.hasEnoughData) constitutionData.principles else emptyList()
        val precautions = if (constitutionData.hasEnoughData) constitutionData.precautions else emptyList()
        val acupointTherapy = if (constitutionData.hasEnoughData) constitutionData.acupointTherapy else ""

        // 根据选中的标签页显示不同内容
        if (constitutionData.hasEnoughData) {
            when (selectedTabIndex) {
                0 -> {
                    // 第一个标签页：今日养动 + 原则 + 切记
                    NursingTabContent1(
                        todayExercise = todayExercise,
                        principles = principles,
                        precautions = precautions
                    )
                }
                1 -> {
                    // 第二个标签页：经穴调养
                    NursingTabContent2(
                        acupointTherapy = acupointTherapy
                    )
                }
            }
        } else {
            // 无数据时显示默认提示
            Text(
                text = "您的体质报告还未生成，请先进行三次把脉测量，开启您的健康养生之旅！",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp,
                    lineHeight = 19.sp
                ),
                color = Color(0xFF666666)
            )
        }
    }
}

/**
 * 调养方案区块
 */
@Composable
private fun NursingSection(
    title: String,
    items: List<String>
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp
            ),
            color = Color(0xFF333333)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        items.forEach { item ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    text = "• ",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp
                    ),
                    color = Color(0xFF333333)
                )
                
                Text(
                    text = item,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp,
                        lineHeight = 19.sp
                    ),
                    color = Color(0xFF333333),
                    modifier = Modifier.weight(1f)
                )
            }
            
            if (item != items.last()) {
                Spacer(modifier = Modifier.height(4.dp))
            }
        }
    }
}

/**
 * 锁定状态调养内容
 */
@Composable
private fun LockedNursingContent() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .clickable(
                onClick = { /* TODO: 跳转订阅页面 */ },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
    ) {
        Image(
            painter = painterResource(id = R.drawable.con_nurselocked),
            contentDescription = "解锁调养方案",
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.Fit
        )
    }
}

@Preview(showBackground = true)
@Composable
fun NursingPlanCardVipPreview() {
    BamaiTheme {
        NursingPlanCard(
            constitutionData = ConstitutionData.mock().copy(isVip = true),
            modifier = Modifier.padding(20.dp)
        )
    }
}

/**
 * 标签页选择器
 */
@Composable
private fun NursingTabSelector(
    selectedIndex: Int,
    onTabSelected: (Int) -> Unit
) {
    val tabs = listOf("运动调养", "经穴调养")

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        tabs.forEachIndexed { index, tab ->
            val isSelected = selectedIndex == index

            Text(
                text = tab,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium
                ),
                color = if (isSelected) Color(0xFF333333) else Color(0xFF999999),
                modifier = Modifier
                    .clickable { onTabSelected(index) }
                    .padding(horizontal = 20.dp, vertical = 8.dp)
            )
        }
    }

    // 下划线指示器
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(2.dp)
    ) {
        Box(
            modifier = Modifier
                .width(40.dp)
                .height(2.dp)
                .background(
                    color = Color(0xFF897457),
                    shape = RoundedCornerShape(1.dp)
                )
                .offset(x = (selectedIndex * 80 + 40).dp) // 根据选中索引调整位置
        )
    }
}

/**
 * 第一个标签页内容：运动调养
 */
@Composable
private fun NursingTabContent1(
    todayExercise: List<String>,
    principles: List<String>,
    precautions: List<String>
) {
    Column {
        // 今日养动
        if (todayExercise.isNotEmpty()) {
            NursingSection(
                title = "今日养动",
                items = todayExercise
            )

            Spacer(modifier = Modifier.height(15.dp))
        }

        // 原则
        if (principles.isNotEmpty()) {
            NursingSection(
                title = "原则",
                items = principles
            )

            Spacer(modifier = Modifier.height(15.dp))
        }

        // 切记
        if (precautions.isNotEmpty()) {
            NursingSection(
                title = "切记",
                items = precautions
            )
        }
    }
}

/**
 * 第二个标签页内容：经穴调养
 */
@Composable
private fun NursingTabContent2(
    acupointTherapy: String
) {
    Column {
        if (acupointTherapy.isNotEmpty()) {
            Text(
                text = "经穴调养",
                style = MaterialTheme.typography.titleSmall.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp
                ),
                color = Color(0xFF333333)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = acupointTherapy,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp,
                    lineHeight = 19.sp
                ),
                color = Color(0xFF333333)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun NursingPlanCardLockedPreview() {
    BamaiTheme {
        NursingPlanCard(
            constitutionData = ConstitutionData.mock().copy(isVip = false),
            modifier = Modifier.padding(20.dp)
        )
    }
}
