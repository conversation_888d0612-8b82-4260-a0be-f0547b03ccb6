package cn.flyok.bamai.ui.measure

import android.util.Log
import kotlin.math.*

/**
 * 非线性HRV指标计算器 - 基于NeuroKit hrv_nonlinear实现
 * 
 * 计算非线性心率变异性指标：
 * - Poincaré图指标 (SD1, SD2)
 * - 熵指标 (SampleEntropy, ApproximateEntropy)
 * - 分形指标 (DFA)
 */
class NonlinearHRV {
    
    companion object {
        private const val TAG = "NonlinearHRV"
    }
    
    /**
     * 非线性HRV指标数据类
     */
    data class NonlinearMetrics(
        // Poincaré图指标
        val sd1: Double,            // 短轴标准差 (ms)
        val sd2: Double,            // 长轴标准差 (ms)
        val sd1sd2Ratio: Double,    // SD1/SD2比值
        val ellipseArea: Double,    // 椭圆面积 (ms²)

        // 熵指标
        val sampleEntropy: Double,      // 样本熵
        val approximateEntropy: Double, // 近似熵

        // 分形指标
        val dfa1: Double,           // 短期去趋势波动分析
        val dfa2: Double,           // 长期去趋势波动分析
        val dfaRatio: Double,       // DFA1/DFA2比值

        // 其他非线性指标
        val recurrenceRate: Double, // 递归率
        val lmax: Double            // 最大线长
    ) : java.io.Serializable
    
    /**
     * 计算非线性HRV指标
     */
    fun calculate(intervals: List<Double>): NonlinearMetrics {
        if (intervals.size < 20) {
            Log.w(TAG, "Insufficient data for nonlinear analysis: ${intervals.size} points")
            return createEmptyMetrics()
        }
        
        Log.d(TAG, "Calculating nonlinear HRV for ${intervals.size} intervals")
        
        // Poincaré图指标
        val sd1 = calculateSD1(intervals)
        val sd2 = calculateSD2(intervals, sd1)
        val sd1sd2Ratio = if (sd2 > 0) sd1 / sd2 else 0.0
        val ellipseArea = PI * sd1 * sd2
        
        // 熵指标
        val sampleEntropy = calculateSampleEntropy(intervals)
        val approximateEntropy = calculateApproximateEntropy(intervals)
        
        // 分形指标
        val dfa1 = calculateDFA(intervals, 4, 16)  // 短期
        val dfa2 = calculateDFA(intervals, 16, 64) // 长期
        val dfaRatio = if (dfa2 > 0) dfa1 / dfa2 else 0.0
        
        // 其他指标
        val recurrenceRate = calculateRecurrenceRate(intervals)
        val lmax = calculateLmax(intervals)
        
        return NonlinearMetrics(
            sd1 = sd1,
            sd2 = sd2,
            sd1sd2Ratio = sd1sd2Ratio,
            ellipseArea = ellipseArea,
            sampleEntropy = sampleEntropy,
            approximateEntropy = approximateEntropy,
            dfa1 = dfa1,
            dfa2 = dfa2,
            dfaRatio = dfaRatio,
            recurrenceRate = recurrenceRate,
            lmax = lmax
        )
    }
    
    /**
     * 计算SD1 (Poincaré图短轴标准差)
     */
    private fun calculateSD1(intervals: List<Double>): Double {
        if (intervals.size < 2) return 0.0
        
        val differences = intervals.zipWithNext { a, b -> b - a }
        val variance = differences.map { it.pow(2) }.average()
        return sqrt(variance / 2.0)
    }
    
    /**
     * 计算SD2 (Poincaré图长轴标准差)
     */
    private fun calculateSD2(intervals: List<Double>, sd1: Double): Double {
        if (intervals.size < 2) return 0.0
        
        val mean = intervals.average()
        val variance = intervals.map { (it - mean).pow(2) }.average()
        val sdnn = sqrt(variance)
        
        return sqrt(2 * sdnn.pow(2) - sd1.pow(2)).coerceAtLeast(0.0)
    }
    
    /**
     * 计算样本熵
     */
    private fun calculateSampleEntropy(intervals: List<Double>, m: Int = 2, r: Double = 0.2): Double {
        if (intervals.size < m + 1) return 0.0
        
        val n = intervals.size
        val tolerance = r * calculateStandardDeviation(intervals)
        
        var matchesM = 0
        var matchesMPlus1 = 0
        
        for (i in 0 until n - m) {
            for (j in i + 1 until n - m) {
                if (isMatch(intervals, i, j, m, tolerance)) {
                    matchesM++
                    if (j < n - m - 1 && isMatch(intervals, i, j, m + 1, tolerance)) {
                        matchesMPlus1++
                    }
                }
            }
        }
        
        return if (matchesMPlus1 > 0) {
            -ln(matchesMPlus1.toDouble() / matchesM.toDouble())
        } else {
            0.0
        }
    }
    
    /**
     * 计算近似熵
     */
    private fun calculateApproximateEntropy(intervals: List<Double>, m: Int = 2, r: Double = 0.2): Double {
        if (intervals.size < m + 1) return 0.0
        
        val n = intervals.size
        val tolerance = r * calculateStandardDeviation(intervals)
        
        fun phi(m: Int): Double {
            var sum = 0.0
            for (i in 0 until n - m + 1) {
                var matches = 0
                for (j in 0 until n - m + 1) {
                    if (isMatch(intervals, i, j, m, tolerance)) {
                        matches++
                    }
                }
                if (matches > 0) {
                    sum += ln(matches.toDouble() / (n - m + 1))
                }
            }
            return sum / (n - m + 1)
        }
        
        return phi(m) - phi(m + 1)
    }
    
    /**
     * 检查两个模式是否匹配
     */
    private fun isMatch(
        data: List<Double>,
        i: Int,
        j: Int,
        m: Int,
        tolerance: Double
    ): Boolean {
        for (k in 0 until m) {
            if (abs(data[i + k] - data[j + k]) > tolerance) {
                return false
            }
        }
        return true
    }
    
    /**
     * 计算去趋势波动分析 (DFA)
     */
    private fun calculateDFA(intervals: List<Double>, minBox: Int, maxBox: Int): Double {
        if (intervals.size < maxBox * 2) return 0.0
        
        // 1. 计算累积和
        val mean = intervals.average()
        val cumulativeSum = mutableListOf<Double>()
        var sum = 0.0
        
        for (interval in intervals) {
            sum += interval - mean
            cumulativeSum.add(sum)
        }
        
        // 2. 计算不同窗口大小的波动
        val boxSizes = mutableListOf<Int>()
        val fluctuations = mutableListOf<Double>()
        
        var boxSize = minBox
        while (boxSize <= maxBox && boxSize < intervals.size / 4) {
            val fluctuation = calculateFluctuation(cumulativeSum, boxSize)
            if (fluctuation > 0) {
                boxSizes.add(boxSize)
                fluctuations.add(fluctuation)
            }
            boxSize = (boxSize * 1.2).toInt() // 指数增长
        }
        
        if (boxSizes.size < 3) return 0.0
        
        // 3. 线性回归计算斜率
        return calculateSlope(
            boxSizes.map { ln(it.toDouble()) },
            fluctuations.map { ln(it) }
        )
    }
    
    /**
     * 计算指定窗口大小的波动
     */
    private fun calculateFluctuation(cumulativeSum: List<Double>, boxSize: Int): Double {
        val n = cumulativeSum.size
        var totalVariance = 0.0
        var boxCount = 0
        
        for (start in 0 until n - boxSize + 1 step boxSize) {
            val end = minOf(start + boxSize, n)
            val box = cumulativeSum.subList(start, end)
            
            if (box.size >= 3) {
                val variance = calculateLinearTrendVariance(box)
                totalVariance += variance
                boxCount++
            }
        }
        
        return if (boxCount > 0) sqrt(totalVariance / boxCount) else 0.0
    }
    
    /**
     * 计算线性趋势方差
     */
    private fun calculateLinearTrendVariance(data: List<Double>): Double {
        val n = data.size
        if (n < 2) return 0.0
        
        val x = (0 until n).map { it.toDouble() }
        val y = data
        
        // 线性回归
        val slope = calculateSlope(x, y)
        val intercept = y.average() - slope * x.average()
        
        // 计算残差方差
        var variance = 0.0
        for (i in data.indices) {
            val predicted = slope * i + intercept
            variance += (data[i] - predicted).pow(2)
        }
        
        return variance / n
    }
    
    /**
     * 计算线性回归斜率
     */
    private fun calculateSlope(x: List<Double>, y: List<Double>): Double {
        if (x.size != y.size || x.size < 2) return 0.0
        
        val n = x.size
        val sumX = x.sum()
        val sumY = y.sum()
        val sumXY = x.zip(y) { xi, yi -> xi * yi }.sum()
        val sumX2 = x.map { it.pow(2) }.sum()
        
        val denominator = n * sumX2 - sumX.pow(2)
        return if (denominator != 0.0) {
            (n * sumXY - sumX * sumY) / denominator
        } else {
            0.0
        }
    }
    
    /**
     * 计算递归率 (简化实现)
     */
    private fun calculateRecurrenceRate(intervals: List<Double>): Double {
        if (intervals.size < 10) return 0.0
        
        val threshold = 0.1 * calculateStandardDeviation(intervals)
        var recurrentPoints = 0
        val n = intervals.size
        
        for (i in intervals.indices) {
            for (j in i + 1 until intervals.size) {
                if (abs(intervals[i] - intervals[j]) < threshold) {
                    recurrentPoints++
                }
            }
        }
        
        val totalPairs = n * (n - 1) / 2
        return if (totalPairs > 0) recurrentPoints.toDouble() / totalPairs else 0.0
    }
    
    /**
     * 计算最大线长 (简化实现)
     */
    private fun calculateLmax(intervals: List<Double>): Double {
        if (intervals.size < 5) return 0.0
        
        val threshold = 0.1 * calculateStandardDeviation(intervals)
        var maxLength = 0
        
        for (i in 0 until intervals.size - 2) {
            var length = 1
            for (j in i + 1 until intervals.size) {
                if (abs(intervals[i] - intervals[j]) < threshold) {
                    length++
                } else {
                    break
                }
            }
            maxLength = maxOf(maxLength, length)
        }
        
        return maxLength.toDouble()
    }
    
    /**
     * 计算标准差
     */
    private fun calculateStandardDeviation(values: List<Double>): Double {
        if (values.size < 2) return 0.0
        
        val mean = values.average()
        val variance = values.map { (it - mean).pow(2) }.average()
        return sqrt(variance)
    }
    
    /**
     * 创建空的指标对象
     */
    private fun createEmptyMetrics(): NonlinearMetrics {
        return NonlinearMetrics(
            sd1 = 0.0, sd2 = 0.0, sd1sd2Ratio = 0.0, ellipseArea = 0.0,
            sampleEntropy = 0.0, approximateEntropy = 0.0,
            dfa1 = 0.0, dfa2 = 0.0, dfaRatio = 0.0,
            recurrenceRate = 0.0, lmax = 0.0
        )
    }
}
