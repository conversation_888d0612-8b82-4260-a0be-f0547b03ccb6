package cn.flyok.bamai.ui.common

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale

/**
 * 通用点击缩放动效修饰符
 * 
 * 功能：
 * - 点击时轻微缩放（默认缩放到0.95）
 * - 松开时恢复原始大小
 * - 动画时长可配置
 * - 缩放比例可配置
 */
@Composable
fun Modifier.clickableWithScale(
    onClick: () -> Unit,
    scaleDown: Float = 0.95f,
    animationDuration: Int = 100,
    enabled: Boolean = true
): Modifier {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) scaleDown else 1f,
        animationSpec = tween(durationMillis = animationDuration),
        label = "scale_animation"
    )
    
    return this
        .scale(scale)
        .clickable(
            onClick = onClick,
            enabled = enabled,
            indication = null,
            interactionSource = interactionSource
        )
}

/**
 * 带自定义交互源的点击缩放动效修饰符
 * 用于需要自定义交互源的场景
 */
@Composable
fun Modifier.clickableWithScale(
    onClick: () -> Unit,
    interactionSource: MutableInteractionSource,
    scaleDown: Float = 0.95f,
    animationDuration: Int = 100,
    enabled: Boolean = true
): Modifier {
    val isPressed by interactionSource.collectIsPressedAsState()
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) scaleDown else 1f,
        animationSpec = tween(durationMillis = animationDuration),
        label = "scale_animation"
    )
    
    return this
        .scale(scale)
        .clickable(
            onClick = onClick,
            enabled = enabled,
            indication = null,
            interactionSource = interactionSource
        )
}

/**
 * 预设的点击动效样式
 */
object ClickEffects {
    /**
     * 轻微缩放 - 适用于大部分按钮
     */
    @Composable
    fun Modifier.lightScale(
        onClick: () -> Unit,
        enabled: Boolean = true
    ): Modifier = clickableWithScale(
        onClick = onClick,
        scaleDown = 0.95f,
        animationDuration = 100,
        enabled = enabled
    )
    
    /**
     * 明显缩放 - 适用于重要按钮
     */
    @Composable
    fun Modifier.mediumScale(
        onClick: () -> Unit,
        enabled: Boolean = true
    ): Modifier = clickableWithScale(
        onClick = onClick,
        scaleDown = 0.9f,
        animationDuration = 120,
        enabled = enabled
    )
    
    /**
     * 强烈缩放 - 适用于特殊按钮
     */
    @Composable
    fun Modifier.strongScale(
        onClick: () -> Unit,
        enabled: Boolean = true
    ): Modifier = clickableWithScale(
        onClick = onClick,
        scaleDown = 0.85f,
        animationDuration = 150,
        enabled = enabled
    )
}
