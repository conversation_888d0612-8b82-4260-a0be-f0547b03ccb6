package cn.flyok.bamai.ui.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.data.homeDataManager
import cn.flyok.bamai.ui.home.components.RecordData
import cn.flyok.bamai.ui.home.components.RecordItem
import cn.flyok.bamai.ui.theme.*

/**
 * 更多记录页面UI - 完全复刻iOS端MoreRecordController
 * 
 * 布局结构：
 * - iOS风格背景色和装饰图片
 * - 自定义导航栏（返回按钮、标题、编辑按钮）
 * - 记录列表（支持编辑模式）
 * - 删除按钮（编辑模式显示）
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MoreRecordScreen(
    onBackClick: () -> Unit,
    onRecordClick: (RecordData) -> Unit,
    modifier: Modifier = Modifier
) {
    // 获取记录数据
    val recordData by homeDataManager.recordData
    val isSubscribed by homeDataManager.isSubscribed
    
    // 编辑状态
    var isEditMode by remember { mutableStateOf(false) }
    var selectedRecords by remember { mutableStateOf(setOf<String>()) }
    
    // 删除确认弹窗状态
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    // iOS风格的背景色
    val backgroundColor = Color(0xFFF8F6F1)
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        // 右上角装饰图片
        Image(
            painter = painterResource(id = R.drawable.home_zzbg),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = 15.dp)
                .size(width = 131.dp, height = 153.dp),
            contentScale = ContentScale.Fit
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding() // 添加顶部安全边距
        ) {
            // 自定义导航栏
            MoreRecordTopBar(
                isEditMode = isEditMode,
                hasData = recordData.isNotEmpty(),
                onBackClick = onBackClick,
                onEditClick = {
                    isEditMode = !isEditMode
                    if (!isEditMode) {
                        selectedRecords = setOf() // 退出编辑模式时清空选择
                    }
                }
            )
            
            // 记录列表
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
            ) {
                if (recordData.isEmpty()) {
                    // 无数据状态
                    RecordItem(
                        hasData = false,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp, vertical = 10.dp)
                    )
                } else {
                    // 有数据状态
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(
                            start = 20.dp,
                            end = 20.dp,
                            top = 10.dp,
                            bottom = if (isEditMode) 80.dp else 20.dp // 编辑模式时为删除按钮预留空间
                        ),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        items(recordData) { record ->
                            RecordItem(
                                pulseName = record.pulseName,
                                timeText = record.timeString,
                                isSubscribed = isSubscribed,
                                isEditMode = isEditMode,
                                isSelected = selectedRecords.contains(record.id),
                                hasData = true,
                                onClick = {
                                    if (isEditMode) {
                                        // 编辑模式：切换选择状态
                                        selectedRecords = if (selectedRecords.contains(record.id)) {
                                            selectedRecords - record.id
                                        } else {
                                            selectedRecords + record.id
                                        }
                                    } else {
                                        // 普通模式：点击查看详情
                                        onRecordClick(record)
                                    }
                                },
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
            }
        }
        
        // 删除按钮（编辑模式显示）
        if (isEditMode && recordData.isNotEmpty()) {
            DeleteButton(
                isEnabled = selectedRecords.isNotEmpty(),
                onClick = { showDeleteDialog = true },
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 20.dp)
            )
        }
    }
    
    // 删除确认弹窗
    if (showDeleteDialog) {
        ConfirmDeleteDialog(
            selectedCount = selectedRecords.size,
            onConfirm = {
                // 执行删除操作
                val selectedCount = selectedRecords.size
                homeDataManager.deleteRecords(selectedRecords.toList())
                selectedRecords = setOf()
                showDeleteDialog = false
                // 如果删除后没有数据了，退出编辑模式
                if (recordData.size == selectedCount) {
                    isEditMode = false
                }
            },
            onDismiss = { showDeleteDialog = false }
        )
    }
}

/**
 * 自定义顶部导航栏 - 对应iOS的导航栏设置
 */
@Composable
fun MoreRecordTopBar(
    isEditMode: Boolean,
    hasData: Boolean,
    onBackClick: () -> Unit,
    onEditClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(44.dp)
            .padding(horizontal = 0.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 返回按钮
        IconButton(
            onClick = onBackClick,
            modifier = Modifier.size(60.dp, 44.dp)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = Color(0xFF333333)
            )
        }

        // 标题
        Text(
            text = "诊脉记录",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333),
            textAlign = TextAlign.Center,
            modifier = Modifier.weight(1f)
        )

        // 编辑按钮
        if (hasData) {
            TextButton(
                onClick = onEditClick,
                modifier = Modifier.size(75.dp, 44.dp)
            ) {
                Text(
                    text = if (isEditMode) "取消" else "编辑",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
            }
        } else {
            // 占位空间，保持布局平衡
            Spacer(modifier = Modifier.size(75.dp, 44.dp))
        }
    }
}

/**
 * 删除按钮 - 对应iOS的删除按钮样式
 */
@Composable
fun DeleteButton(
    isEnabled: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(280.dp)
            .height(48.dp)
            .alpha(if (isEnabled) 1f else 0.5f), // 使用透明度表示禁用状态
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFAE8772)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Card(
            modifier = Modifier
                .fillMaxSize()
                .padding(5.dp),
            shape = RoundedCornerShape(19.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFAE8772) // 保持原色，通过透明度控制
            ),
            border = androidx.compose.foundation.BorderStroke(
                width = 1.dp,
                color = Color(0xFFE5D6CE)
            )
        ) {
            Button(
                onClick = onClick,
                enabled = isEnabled,
                modifier = Modifier.fillMaxSize(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Transparent,
                    disabledContainerColor = Color.Transparent
                ),
                contentPadding = PaddingValues(0.dp)
            ) {
                Text(
                    text = "删除数据",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White // 保持白色，通过透明度控制
                )
            }
        }
    }
}

/**
 * 删除确认弹窗 - 对应iOS的ConfirmDeleteView
 */
@Composable
fun ConfirmDeleteDialog(
    selectedCount: Int,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "确认删除",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF333333)
            )
        },
        text = {
            Text(
                text = if (selectedCount > 1) {
                    "确定要删除选中的 $selectedCount 条记录吗？"
                } else {
                    "确定要删除这条记录吗？"
                },
                fontSize = 16.sp,
                color = Color(0xFF666666)
            )
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm
            ) {
                Text(
                    text = "删除",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFFAE8772)
                )
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text(
                    text = "取消",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF666666)
                )
            }
        },
        containerColor = Color(0xFFFFFFFF),
        shape = RoundedCornerShape(12.dp)
    )
}

@Preview(showBackground = true)
@Composable
fun MoreRecordScreenPreview() {
    BamaiNoRippleTheme {
        MoreRecordScreen(
            onBackClick = {},
            onRecordClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun MoreRecordTopBarPreview() {
    BamaiNoRippleTheme {
        MoreRecordTopBar(
            isEditMode = false,
            hasData = true,
            onBackClick = {},
            onEditClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun DeleteButtonPreview() {
    BamaiNoRippleTheme {
        DeleteButton(
            isEnabled = true,
            onClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ConfirmDeleteDialogPreview() {
    BamaiNoRippleTheme {
        ConfirmDeleteDialog(
            selectedCount = 2,
            onConfirm = {},
            onDismiss = {}
        )
    }
}
