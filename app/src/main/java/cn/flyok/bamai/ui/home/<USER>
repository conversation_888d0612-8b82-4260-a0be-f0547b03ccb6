package cn.flyok.bamai.ui.home

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import cn.flyok.bamai.data.homeDataManager
import cn.flyok.bamai.ui.home.components.RecordData
import cn.flyok.bamai.ui.measure.PulseDetailActivity
import cn.flyok.bamai.ui.theme.BamaiNoRippleTheme

/**
 * 更多记录页面Activity - 完全复刻iOS端MoreRecordController
 * 
 * 功能包括：
 * - 显示所有测量记录的完整列表
 * - 支持编辑模式（批量选择和删除）
 * - 支持左滑删除单条记录
 * - 删除确认弹窗
 * - 订阅状态控制
 * - 点击记录查看详情或跳转订阅页面
 */
class MoreRecordActivity : ComponentActivity() {

    companion object {
        private const val TAG = "MoreRecordActivity"

        /**
         * 创建启动Intent
         */
        fun createIntent(context: Context): Intent {
            return Intent(context, MoreRecordActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            BamaiNoRippleTheme {
                Surface(
                    modifier = Modifier.fillMaxSize()
                ) {
                    MoreRecordScreen(
                        onBackClick = { finish() },
                        onRecordClick = { record ->
                            // 根据订阅状态跳转到详情页面或订阅页面
                            if (homeDataManager.isSubscribed.value) {
                                // 已订阅：跳转到详情页面
                                val pulseRecord = homeDataManager.getPulseRecordById(record.id)
                                if (pulseRecord != null) {
                                    // 创建简化的数据模型，避免序列化问题
                                    val simplifiedModel = cn.flyok.bamai.ui.measure.PulseResultModel(
                                        heartRate = pulseRecord.pulseResultModel.heartRate,
                                        pulseType = pulseRecord.pulseResultModel.pulseType,
                                        pulseFeatures = pulseRecord.pulseResultModel.pulseFeatures,
                                        diagnosis = pulseRecord.pulseResultModel.diagnosis,
                                        suggestions = pulseRecord.pulseResultModel.suggestions,
                                        heartRateData = emptyList(), // 简化：不传递大量数据
                                        measurementTime = pulseRecord.pulseResultModel.measurementTime,

                                        // iOS格式的数据字段
                                        rateDescription = pulseRecord.pulseResultModel.rateDescription,
                                        strengthDescription = pulseRecord.pulseResultModel.strengthDescription,
                                        trendDescription = pulseRecord.pulseResultModel.trendDescription,
                                        pulseFeatureText = pulseRecord.pulseResultModel.pulseFeatureText,
                                        diagnosisSections = pulseRecord.pulseResultModel.diagnosisSections,
                                        dietAdvice = pulseRecord.pulseResultModel.dietAdvice,
                                        lifestyleAdvice = pulseRecord.pulseResultModel.lifestyleAdvice,
                                        exerciseAdvice = pulseRecord.pulseResultModel.exerciseAdvice,
                                        emotionalAdvice = pulseRecord.pulseResultModel.emotionalAdvice
                                    )
                                    val detailIntent = PulseDetailActivity.createIntent(this@MoreRecordActivity, simplifiedModel, record.id)
                                    startActivity(detailIntent)
                                }
                            } else {
                                // 未订阅：跳转到订阅页面
                                // TODO: 实现订阅页面跳转，这里预留接口
                                // 临时切换订阅状态用于演示
                                homeDataManager.toggleSubscription()
                            }
                        }
                    )
                }
            }
        }
    }
}
