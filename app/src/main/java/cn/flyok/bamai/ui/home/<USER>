package cn.flyok.bamai.ui.home

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cn.flyok.bamai.data.homeDataManager
import cn.flyok.bamai.ui.home.components.HomeHeader
import cn.flyok.bamai.ui.home.components.ResultCard
import cn.flyok.bamai.ui.home.components.TimeCard
import cn.flyok.bamai.ui.home.components.RecordItem
import cn.flyok.bamai.ui.measure.HeartRateMeasureActivity
import cn.flyok.bamai.ui.measure.PulseDetailActivity
import cn.flyok.bamai.ui.theme.*

/**
 * 首页主界面 - 完全复刻iOS端HomeController
 * 
 * 结构说明：
 * - Section 0: HomeHeader (首页头部)
 * - Section 1: TimeCard (十二时辰养生)
 * - Section 2: RecordList (记录列表)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidth = configuration.screenWidthDp.dp
    val screenHeight = configuration.screenHeightDp.dp

    // 获取数据管理器的状态
    val isSubscribed by homeDataManager.isSubscribed
    val recordData by homeDataManager.recordData
    val currentTimeData by homeDataManager.currentTimeData

    // 心率测量Activity启动器
    val heartRateLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val heartRate = result.data?.getIntExtra(
                HeartRateMeasureActivity.EXTRA_HEART_RATE_RESULT,
                0
            ) ?: 0
            if (heartRate > 0) {
                homeDataManager.processMeasurementResult(heartRate)
            }
        }
    }
    
    // 计算iOS端的头部高度比例 (277/375 * 屏幕宽度)
    val headerHeight = with(density) {
        (277f / 375f * screenWidth.toPx()).toDp()
    }
    
    // LazyColumn状态，用于监听滚动
    val listState = rememberLazyListState()
    
    // 计算导航栏透明度 - 完全复刻iOS端的渐变逻辑
    val navAlpha by remember {
        derivedStateOf {
            // iOS端计算逻辑：imgHeight - navHeight = maxHeight
            val imgHeight = headerHeight
            val navHeight = 44.dp + BamaiDimensions.navBarHeight // 状态栏 + 导航栏
            val maxHeight = imgHeight - navHeight

            // 获取滚动偏移量（像素值）
            val scrollOffsetPx = if (listState.firstVisibleItemIndex == 0) {
                listState.firstVisibleItemScrollOffset
            } else {
                // 如果滚动超过第一个item，直接显示完全不透明
                Int.MAX_VALUE
            }

            val scrollOffsetDp = with(density) { scrollOffsetPx.toDp() }

            when {
                scrollOffsetDp >= maxHeight -> 1f
                scrollOffsetDp <= 0.dp -> 0f
                else -> (scrollOffsetDp / maxHeight).coerceIn(0f, 1f)
            }
        }
    }
    
    Box(modifier = modifier.fillMaxSize()) {
        // 主内容区域
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background),
            contentPadding = PaddingValues(
                bottom = 60.dp // 为底部TabBar预留空间（与TabBar高度一致）
            )
        ) {
            // Section 0: 首页头部
            item {
                HomeHeader(
                    pulseResult = homeDataManager.getPulseResultText(),
                    timeText = homeDataManager.getPulseTimeText(),
                    logoRes = homeDataManager.getPulseLogoResource(),
                    onMeasureClick = {
                        // 启动心率测量Activity
                        val intent = HeartRateMeasureActivity.createIntent(context)
                        heartRateLauncher.launch(intent)
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            // Section 1: 脉象结果卡片
            item {
                val (suitableText, forbiddenText) = homeDataManager.getSuitableForbiddenTexts()
                ResultCard(
                    suitableText = suitableText,
                    forbiddenText = forbiddenText,
                    description = homeDataManager.getPulseDescription(),
                    isSubscribed = isSubscribed,
                    hasData = homeDataManager.hasData(),
                    onMoreClick = {
                        // 跳转到脉象详情页面
                        if (homeDataManager.hasData()) {
                            val latestRecord = homeDataManager.getLatestPulseRecord()
                            if (latestRecord != null) {
                                // 创建简化的数据模型，避免序列化问题
                                val simplifiedModel = cn.flyok.bamai.ui.measure.PulseResultModel(
                                    heartRate = latestRecord.pulseResultModel.heartRate,
                                    pulseType = latestRecord.pulseResultModel.pulseType,
                                    pulseFeatures = latestRecord.pulseResultModel.pulseFeatures,
                                    diagnosis = latestRecord.pulseResultModel.diagnosis,
                                    suggestions = latestRecord.pulseResultModel.suggestions,
                                    heartRateData = emptyList(), // 简化：不传递大量数据
                                    timestamps = emptyList(),
                                    measurementDuration = latestRecord.pulseResultModel.measurementDuration,
                                    hrvAnalysis = null, // 简化：不传递复杂对象
                                    ppgSignalData = emptyList(),
                                    detectedPeaks = emptyList(),
                                    pulseAnalysis = null,
                                    waveformData = null,
                                    heartbeatCycles = emptyList(),
                                    averageWaveform = null,
                                    measurementTime = latestRecord.pulseResultModel.measurementTime,

                                    // iOS格式的数据字段
                                    rateDescription = latestRecord.pulseResultModel.rateDescription,
                                    strengthDescription = latestRecord.pulseResultModel.strengthDescription,
                                    trendDescription = latestRecord.pulseResultModel.trendDescription,
                                    pulseFeatureText = latestRecord.pulseResultModel.pulseFeatureText,
                                    diagnosisSections = latestRecord.pulseResultModel.diagnosisSections,
                                    dietAdvice = latestRecord.pulseResultModel.dietAdvice,
                                    lifestyleAdvice = latestRecord.pulseResultModel.lifestyleAdvice,
                                    exerciseAdvice = latestRecord.pulseResultModel.exerciseAdvice,
                                    emotionalAdvice = latestRecord.pulseResultModel.emotionalAdvice
                                )
                                val detailIntent = cn.flyok.bamai.ui.measure.PulseDetailActivity.createIntent(context, simplifiedModel, latestRecord.id)
                                context.startActivity(detailIntent)
                            }
                        }
                    },
                    onLockClick = {
                        // TODO: 实现解锁功能 - 跳转到订阅页面
                        // 临时切换订阅状态用于演示
                        homeDataManager.toggleSubscription()
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = BamaiDimensions.cardMarginHorizontal,
                            vertical = BamaiDimensions.cardMarginVertical
                        )
                )
            }
            
            // Section 2: 十二时辰卡片
            item {
                Column {
                    SectionHeader(
                        title = "十二时辰养生",
                        showMoreButton = false,
                        modifier = Modifier.padding(horizontal = BamaiDimensions.cardMarginHorizontal)
                    )

                    TimeCard(
                        timeText = currentTimeData.timeRange,
                        titleText = currentTimeData.title,
                        descriptionText = currentTimeData.description,
                        timeCharacters = currentTimeData.characters,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                horizontal = BamaiDimensions.cardMarginHorizontal,
                                vertical = BamaiDimensions.cardMarginVertical
                            )
                    )
                }
            }
            
            // Section 3: 记录列表
            item {
                Column {
                    SectionHeader(
                        title = "记录",
                        showMoreButton = recordData.isNotEmpty(),
                        modifier = Modifier.padding(horizontal = BamaiDimensions.cardMarginHorizontal)
                    )

                    // 显示记录列表或无数据状态
                    if (recordData.isEmpty()) {
                        RecordItem(
                            hasData = false,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    horizontal = BamaiDimensions.cardMarginHorizontal,
                                    vertical = BamaiDimensions.cardMarginVertical
                                )
                        )
                    } else {
                        // 显示最新10条记录
                        val displayRecords = recordData.take(10)
                        displayRecords.forEach { record ->
                            RecordItem(
                                pulseName = record.pulseName,
                                timeText = record.timeString,
                                isSubscribed = isSubscribed,
                                hasData = true,
                                onClick = {
                                    if (isSubscribed) {
                                        // 跳转到记录详情页面
                                        val pulseRecord = homeDataManager.getPulseRecordById(record.id)
                                        if (pulseRecord != null) {
                                            // 创建简化的数据模型，避免序列化问题
                                            val simplifiedModel = cn.flyok.bamai.ui.measure.PulseResultModel(
                                                heartRate = pulseRecord.pulseResultModel.heartRate,
                                                pulseType = pulseRecord.pulseResultModel.pulseType,
                                                pulseFeatures = pulseRecord.pulseResultModel.pulseFeatures,
                                                diagnosis = pulseRecord.pulseResultModel.diagnosis,
                                                suggestions = pulseRecord.pulseResultModel.suggestions,
                                                heartRateData = emptyList(), // 简化：不传递大量数据
                                                timestamps = emptyList(), // 简化：不传递大量数据
                                                measurementDuration = pulseRecord.pulseResultModel.measurementDuration,
                                                measurementTime = pulseRecord.pulseResultModel.measurementTime,

                                                // iOS格式的数据字段
                                                rateDescription = pulseRecord.pulseResultModel.rateDescription,
                                                strengthDescription = pulseRecord.pulseResultModel.strengthDescription,
                                                trendDescription = pulseRecord.pulseResultModel.trendDescription,
                                                pulseFeatureText = pulseRecord.pulseResultModel.pulseFeatureText,
                                                diagnosisSections = pulseRecord.pulseResultModel.diagnosisSections,
                                                dietAdvice = pulseRecord.pulseResultModel.dietAdvice,
                                                lifestyleAdvice = pulseRecord.pulseResultModel.lifestyleAdvice,
                                                exerciseAdvice = pulseRecord.pulseResultModel.exerciseAdvice,
                                                emotionalAdvice = pulseRecord.pulseResultModel.emotionalAdvice
                                                // 不传递复杂的对象，避免序列化问题
                                            )
                                            val detailIntent = PulseDetailActivity.createIntent(context, simplifiedModel)
                                            context.startActivity(detailIntent)
                                        }
                                    } else {
                                        // TODO: 跳转到订阅页面
                                        homeDataManager.toggleSubscription()
                                    }
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(
                                        horizontal = BamaiDimensions.cardMarginHorizontal,
                                        vertical = BamaiDimensions.cardMarginVertical
                                    )
                            )
                        }
                    }
                }
            }
        }
        
        // 自定义导航栏 - 支持渐变透明度
        CustomNavigationBar(
            alpha = navAlpha,
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}

/**
 * 自定义导航栏 - 完全复刻iOS端的导航栏效果
 *
 * 特点：
 * - 支持透明度渐变
 * - 状态栏 + 导航栏高度
 * - 左侧标题"把脉"
 * - 背景色与主题背景色一致
 */
@Composable
private fun CustomNavigationBar(
    alpha: Float,
    modifier: Modifier = Modifier
) {
    // 状态栏高度（Android系统状态栏）
    val statusBarHeight = 44.dp

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(statusBarHeight + BamaiDimensions.navBarHeight)
            .background(
                BamaiViewBackColor.copy(alpha = alpha)
            )
    ) {
        // 导航栏内容区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(BamaiDimensions.navBarHeight)
                .align(Alignment.BottomCenter)
        ) {
            Text(
                text = "把脉",
                style = MaterialTheme.typography.headlineMedium,
                color = BamaiPrimaryText.copy(alpha = alpha),
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = BamaiDimensions.paddingXLarge)
            )
        }
    }
}

/**
 * 分区标题组件
 */
@Composable
private fun SectionHeader(
    title: String,
    showMoreButton: Boolean,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = BamaiDimensions.paddingMedium),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onBackground
        )
        
        if (showMoreButton) {
            TextButton(
                onClick = {
                    // 跳转到更多记录页面
                    val intent = MoreRecordActivity.createIntent(context)
                    context.startActivity(intent)
                }
            ) {
                Text(
                    text = "更多",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary
                )
            }
        }
    }
}

// 临时占位符组件，后续会被实际组件替换







@Preview(showBackground = true)
@Composable
fun HomeScreenPreview() {
    BamaiTheme {
        HomeScreen()
    }
}
