package cn.flyok.bamai.ui.measure

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 专业HRV分析结果展示组件
 *
 * 基于医学标准设计的心率变异性分析结果展示界面
 */
@Composable
fun HRVResultDisplay(
    hrvResult: HRVAnalyzer.HRVAnalysisResult,
    modifier: Modifier = Modifier
) {
    // 专业医学界面配色
    val primaryColor = Color(0xFF2E7D32)      // 医学绿
    val backgroundColor = Color(0xFFF8F9FA)    // 浅灰背景
    val cardColor = Color.White
    val textPrimary = Color(0xFF212121)
    val textSecondary = Color(0xFF757575)

    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .background(backgroundColor),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        // 标题
        item {
            Text(
                text = "心率变异性分析报告",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = primaryColor,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Text(
                text = "Heart Rate Variability Analysis",
                fontSize = 14.sp,
                color = textSecondary,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }

        // 综合评估 - 突出显示
        item {
            ProfessionalOverallCard(hrvResult.assessment, primaryColor, cardColor, textPrimary)
        }

        // 核心指标概览
        item {
            CoreMetricsOverview(hrvResult, primaryColor, cardColor, textPrimary, textSecondary)
        }

        // 详细指标分析
        item {
            DetailedMetricsCard(hrvResult, cardColor, textPrimary, textSecondary)
        }

        // 临床解读和建议
        item {
            ClinicalInterpretationCard(hrvResult, primaryColor, cardColor, textPrimary, textSecondary)
        }

        // 数据质量报告
        item {
            DataQualityReportCard(hrvResult.dataQuality, cardColor, textPrimary, textSecondary)
        }
    }
}

@Composable
private fun ProfessionalOverallCard(
    assessment: HRVAnalyzer.HRVAssessment,
    primaryColor: Color,
    cardColor: Color,
    textPrimary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 评分圆环和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "综合评分",
                        fontSize = 16.sp,
                        color = textPrimary,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "${assessment.overallScore.toInt()}",
                        fontSize = 36.sp,
                        fontWeight = FontWeight.Bold,
                        color = getScoreColor(assessment.overallScore)
                    )
                    Text(
                        text = "/ 100",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = "压力水平",
                        fontSize = 14.sp,
                        color = textPrimary
                    )
                    Text(
                        text = getStressLevelText(assessment.stressLevel),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = getStressLevelColor(assessment.stressLevel)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 自主神经平衡指示器
            Column {
                Text(
                    text = "自主神经平衡 (LF/HF)",
                    fontSize = 14.sp,
                    color = textPrimary,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = String.format("%.2f", assessment.autonomicBalance),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = primaryColor
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = getBalanceInterpretation(assessment.autonomicBalance),
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

@Composable
private fun CoreMetricsOverview(
    hrvResult: HRVAnalyzer.HRVAnalysisResult,
    primaryColor: Color,
    cardColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "核心指标",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 关键指标网格
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                CoreMetricItem(
                    title = "RMSSD",
                    value = "${hrvResult.timeDomain.rmssd.toInt()}",
                    unit = "ms",
                    description = "副交感神经活性",
                    color = primaryColor,
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )

                CoreMetricItem(
                    title = "SDNN",
                    value = "${hrvResult.timeDomain.sdNN.toInt()}",
                    unit = "ms",
                    description = "总体变异性",
                    color = primaryColor,
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )

                CoreMetricItem(
                    title = "pNN50",
                    value = "${hrvResult.timeDomain.pnn50.toInt()}",
                    unit = "%",
                    description = "短期变异性",
                    color = primaryColor,
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )
            }
        }
    }
}

@Composable
private fun CoreMetricItem(
    title: String,
    value: String,
    unit: String,
    description: String,
    color: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.width(100.dp)
    ) {
        Text(
            text = title,
            fontSize = 12.sp,
            color = textSecondary,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(4.dp))
        Row(
            verticalAlignment = Alignment.Bottom
        ) {
            Text(
                text = value,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = color
            )
            Text(
                text = unit,
                fontSize = 12.sp,
                color = textSecondary,
                modifier = Modifier.padding(bottom = 2.dp)
            )
        }
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = description,
            fontSize = 10.sp,
            color = textSecondary,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun DetailedMetricsCard(
    hrvResult: HRVAnalyzer.HRVAnalysisResult,
    cardColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "详细指标分析",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 时域指标
            MetricSection(
                title = "时域指标 (Time Domain)",
                metrics = listOf(
                    MetricData("平均RR间隔", "${hrvResult.timeDomain.meanNN.toInt()}", "ms", "心跳间隔平均值"),
                    MetricData("SDNN", "${hrvResult.timeDomain.sdNN.toInt()}", "ms", "总体变异性指标"),
                    MetricData("RMSSD", "${hrvResult.timeDomain.rmssd.toInt()}", "ms", "副交感神经活性"),
                    MetricData("pNN50", "${hrvResult.timeDomain.pnn50.toInt()}", "%", "短期变异性指标")
                ),
                textPrimary = textPrimary,
                textSecondary = textSecondary
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 频域指标
            MetricSection(
                title = "频域指标 (Frequency Domain)",
                metrics = listOf(
                    MetricData("总功率", "${hrvResult.frequency.totalPower.toInt()}", "ms²", "总体功率谱"),
                    MetricData("LF功率", "${hrvResult.frequency.lf.toInt()}", "ms²", "低频功率(0.04-0.15Hz)"),
                    MetricData("HF功率", "${hrvResult.frequency.hf.toInt()}", "ms²", "高频功率(0.15-0.4Hz)"),
                    MetricData("LF/HF比值", String.format("%.2f", hrvResult.frequency.lfhfRatio), "", "自主神经平衡")
                ),
                textPrimary = textPrimary,
                textSecondary = textSecondary
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 非线性指标
            MetricSection(
                title = "非线性指标 (Nonlinear)",
                metrics = listOf(
                    MetricData("SD1", "${hrvResult.nonlinear.sd1.toInt()}", "ms", "Poincaré图短轴"),
                    MetricData("SD2", "${hrvResult.nonlinear.sd2.toInt()}", "ms", "Poincaré图长轴"),
                    MetricData("SD1/SD2", String.format("%.2f", hrvResult.nonlinear.sd1sd2Ratio), "", "变异性比值"),
                    MetricData("样本熵", String.format("%.3f", hrvResult.nonlinear.sampleEntropy), "", "信号复杂度")
                ),
                textPrimary = textPrimary,
                textSecondary = textSecondary
            )
        }
    }
}

data class MetricData(
    val name: String,
    val value: String,
    val unit: String,
    val description: String
)

@Composable
private fun MetricSection(
    title: String,
    metrics: List<MetricData>,
    textPrimary: Color,
    textSecondary: Color
) {
    Column {
        Text(
            text = title,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = textPrimary
        )
        Spacer(modifier = Modifier.height(8.dp))

        metrics.forEach { metric ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = metric.name,
                        fontSize = 13.sp,
                        color = textPrimary,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = metric.description,
                        fontSize = 11.sp,
                        color = textSecondary
                    )
                }

                Text(
                    text = "${metric.value} ${metric.unit}".trim(),
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Bold,
                    color = textPrimary
                )
            }
        }
    }
}

@Composable
private fun ClinicalInterpretationCard(
    hrvResult: HRVAnalyzer.HRVAnalysisResult,
    primaryColor: Color,
    cardColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "临床解读与建议",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 解读说明
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        Color(0xFFF5F5F5),
                        RoundedCornerShape(8.dp)
                    )
                    .padding(16.dp)
            ) {
                Text(
                    text = hrvResult.assessment.interpretation,
                    fontSize = 14.sp,
                    color = textPrimary,
                    lineHeight = 20.sp
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 健康建议
            if (hrvResult.recommendations.isNotEmpty()) {
                Text(
                    text = "个性化建议",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = textPrimary
                )

                Spacer(modifier = Modifier.height(8.dp))

                hrvResult.recommendations.forEach { recommendation ->
                    Row(
                        modifier = Modifier.padding(vertical = 4.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(6.dp)
                                .background(primaryColor, RoundedCornerShape(3.dp))
                                .padding(top = 6.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = recommendation,
                            fontSize = 13.sp,
                            color = textPrimary,
                            lineHeight = 18.sp,
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DataQualityReportCard(
    dataQuality: HRVAnalyzer.DataQuality,
    cardColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "数据质量报告",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                QualityMetricItem(
                    title = "质量评分",
                    value = "${(dataQuality.score * 100).toInt()}%",
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )

                QualityMetricItem(
                    title = "测量时长",
                    value = "${dataQuality.duration.toInt()}s",
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )

                QualityMetricItem(
                    title = "心跳数",
                    value = "${dataQuality.intervalCount}",
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )

                QualityMetricItem(
                    title = "异常率",
                    value = "${(dataQuality.artifactRate * 100).toInt()}%",
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )
            }
        }
    }
}

@Composable
private fun QualityMetricItem(
    title: String,
    value: String,
    textPrimary: Color,
    textSecondary: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = textPrimary
        )
        Text(
            text = title,
            fontSize = 12.sp,
            color = textSecondary
        )
    }
}

// 辅助函数
private fun getScoreColor(score: Double): Color {
    return when {
        score >= 80 -> Color(0xFF2E7D32) // 深绿色
        score >= 60 -> Color(0xFF388E3C) // 绿色
        score >= 40 -> Color(0xFFFF8F00) // 橙色
        else -> Color(0xFFD32F2F) // 红色
    }
}

private fun getStressLevelText(level: HRVAnalyzer.StressLevel): String {
    return when (level) {
        HRVAnalyzer.StressLevel.LOW -> "低压力"
        HRVAnalyzer.StressLevel.MODERATE -> "中等压力"
        HRVAnalyzer.StressLevel.HIGH -> "高压力"
        HRVAnalyzer.StressLevel.VERY_HIGH -> "极高压力"
        HRVAnalyzer.StressLevel.UNKNOWN -> "未知"
    }
}

private fun getStressLevelColor(level: HRVAnalyzer.StressLevel): Color {
    return when (level) {
        HRVAnalyzer.StressLevel.LOW -> Color(0xFF2E7D32)
        HRVAnalyzer.StressLevel.MODERATE -> Color(0xFF388E3C)
        HRVAnalyzer.StressLevel.HIGH -> Color(0xFFFF8F00)
        HRVAnalyzer.StressLevel.VERY_HIGH -> Color(0xFFD32F2F)
        HRVAnalyzer.StressLevel.UNKNOWN -> Color.Gray
    }
}

private fun getBalanceInterpretation(lfhfRatio: Double): String {
    return when {
        lfhfRatio < 0.5 -> "副交感神经占主导"
        lfhfRatio > 2.5 -> "交感神经占主导"
        else -> "自主神经平衡良好"
    }
}
