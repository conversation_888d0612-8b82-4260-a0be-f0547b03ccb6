package cn.flyok.bamai.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext

// 把脉App专用颜色方案 - 完全复刻iOS端
private val BamaiColorScheme = lightColorScheme(
    primary = BamaiButtonColor,           // 主色调 - 按钮颜色
    onPrimary = Color.White,              // 主色调上的文字
    secondary = BamaiSecondaryText,       // 次要色调 - 副文字颜色
    onSecondary = Color.White,            // 次要色调上的文字
    tertiary = BamaiTimeTitle,            // 第三色调 - 时辰标题颜色
    onTertiary = Color.White,             // 第三色调上的文字
    background = BamaiViewBackColor,      // 背景色
    onBackground = BamaiPrimaryText,      // 背景上的文字
    surface = BamaiCardBackground,        // 表面色 - 卡片背景
    onSurface = BamaiPrimaryText,         // 表面上的文字
    surfaceVariant = BamaiBorderColor,    // 表面变体 - 边框颜色
    onSurfaceVariant = BamaiSecondaryText, // 表面变体上的文字
    outline = BamaiBorderColor,           // 轮廓色
    outlineVariant = BamaiButtonBorder    // 轮廓变体
)

// 保留原有的颜色方案用于兼容
private val DarkColorScheme = darkColorScheme(
    primary = Purple80,
    secondary = PurpleGrey80,
    tertiary = Pink80
)

private val LightColorScheme = lightColorScheme(
    primary = Purple40,
    secondary = PurpleGrey40,
    tertiary = Pink40
)

@Composable
fun BamaiTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // 把脉App始终使用专用颜色方案，不使用动态颜色
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    // 把脉App专用主题 - 始终使用我们定义的颜色方案
    val colorScheme = BamaiColorScheme

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}