package cn.flyok.bamai

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import cn.flyok.bamai.ui.main.MainScreen
import cn.flyok.bamai.ui.privacy.PrivacyAgreementActivity
import cn.flyok.bamai.ui.theme.BamaiNoRippleTheme

/**
 * 把脉App主Activity
 * 完全复刻iOS端的UI和交互体验，包含TabBar导航
 */
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 检查是否首次启动
        if (PrivacyAgreementActivity.isFirstLaunch(this)) {
            // 首次启动，跳转到隐私政策同意页面
            val intent = PrivacyAgreementActivity.createIntent(this)
            startActivity(intent)
            finish()
            return
        }

        setContent {
            BamaiNoRippleTheme {
                Surface(
                    modifier = Modifier.fillMaxSize()
                ) {
                    MainScreen()
                }
            }
        }
    }
}