package cn.flyok.bamai.data

import android.content.Context
import android.content.SharedPreferences
import androidx.compose.runtime.*
import cn.flyok.bamai.ui.home.components.*
import cn.flyok.bamai.ui.measure.PulseResultModel
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.text.SimpleDateFormat
import java.util.*

/**
 * 五元组数据类
 */
data class Tuple5<A, B, C, D, E>(
    val first: A,
    val second: B,
    val third: C,
    val fourth: D,
    val fifth: E
)

/**
 * 首页数据管理器 - 管理首页所有数据状态
 * 复刻iOS端的数据管理逻辑，支持数据持久化
 */
class HomeDataManager(private val context: Context) {

    companion object {
        private const val PREFS_NAME = "bamai_pulse_records"
        private const val KEY_PULSE_RECORDS = "pulse_records"
        private const val KEY_SUBSCRIPTION_STATUS = "subscription_status"
    }

    private val sharedPrefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    // 用户订阅状态
    private val _isSubscribed = mutableStateOf(loadSubscriptionStatus())
    val isSubscribed: State<Boolean> = _isSubscribed

    // 脉象记录数据
    private val _recordData = mutableStateOf<List<RecordData>>(emptyList())
    val recordData: State<List<RecordData>> = _recordData

    // 完整脉象记录数据
    private val _pulseRecords = mutableStateOf<List<PulseRecord>>(emptyList())
    val pulseRecords: State<List<PulseRecord>> = _pulseRecords

    // 当前脉象结果
    private val _currentPulseResult = mutableStateOf<PulseData?>(null)
    val currentPulseResult: State<PulseData?> = _currentPulseResult

    // 十二时辰数据
    private val _currentTimeData = mutableStateOf(getCurrentTimeDataByHour())
    val currentTimeData: State<TimeData> = _currentTimeData

    init {
        // 初始化时加载持久化数据
        android.util.Log.d("HomeDataManager", "Initializing HomeDataManager...")
        loadPulseRecords()
        android.util.Log.d("HomeDataManager", "HomeDataManager initialized with ${_pulseRecords.value.size} records")
    }
    
    /**
     * 获取当前脉象结果显示文本
     */
    fun getPulseResultText(): String {
        return _currentPulseResult.value?.pulseName ?: "暂无数据"
    }
    
    /**
     * 获取当前脉象时间文本
     */
    fun getPulseTimeText(): String {
        return _currentPulseResult.value?.timeString ?: "--"
    }
    
    /**
     * 获取当前脉象Logo资源
     */
    fun getPulseLogoResource(): Int {
        return getHomeLogoResource(_currentPulseResult.value?.pulseType ?: PulseType.NONE)
    }
    
    /**
     * 获取脉象描述文本
     */
    fun getPulseDescription(): String {
        return if (_recordData.value.isEmpty()) {
            "暂时还没有数据哦，点击上方按钮，获取您的第一份脉象说明"
        } else {
            _currentPulseResult.value?.pulseFeatures ?: "脉搏藏在深处，轻按不应，重按始得。多见于里证（邪气在脏腑），如气滞、血瘀、寒证、水湿停滞等"
        }
    }
    
    /**
     * 获取宜忌文本
     */
    fun getSuitableForbiddenTexts(): Pair<String, String> {
        val pulse = _currentPulseResult.value
        return if (pulse != null && pulse.suitable.isNotEmpty() && pulse.forbidden.isNotEmpty()) {
            Pair(
                pulse.suitable.randomOrNull() ?: "--",
                pulse.forbidden.randomOrNull() ?: "--"
            )
        } else {
            Pair("--", "--")
        }
    }
    
    /**
     * 是否有脉象数据
     */
    fun hasData(): Boolean {
        return _recordData.value.isNotEmpty()
    }
    
    /**
     * 处理心率测量结果
     * @param heartRate 测量得到的心率值
     */
    fun processMeasurementResult(heartRate: Int) {
        // 根据心率判断脉象类型
        val (pulseName, pulseType, pulseFeatures, suitable, forbidden) = when {
            heartRate < 60 -> {
                Tuple5(
                    "迟脉",
                    PulseType.CHI_MAI,
                    "脉搏缓慢，每分钟少于60次，多见于体质虚寒或心脏疾病",
                    listOf("温补食物", "适度运动", "保暖"),
                    listOf("生冷食物", "过度劳累", "熬夜")
                )
            }
            heartRate > 100 -> {
                Tuple5(
                    "数脉",
                    PulseType.SHU_MAI,
                    "脉搏急促，每分钟超过100次，多见于发热、情绪激动或甲亢",
                    listOf("清淡饮食", "静心养神", "充足睡眠"),
                    listOf("辛辣刺激", "剧烈运动", "情绪激动")
                )
            }
            else -> {
                Tuple5(
                    "平脉",
                    PulseType.PING_MAI,
                    "脉象平和，不浮不沉，不快不慢，节律整齐，是健康人的正常脉象",
                    listOf("清淡饮食", "适量运动", "规律作息"),
                    listOf("暴饮暴食", "熬夜", "过度劳累")
                )
            }
        }

        val newRecord = RecordData(
            id = UUID.randomUUID().toString(),
            pulseName = pulseName,
            timeString = getCurrentTimeString(),
            pulseType = pulseType
        )

        val newPulseData = PulseData(
            pulseName = pulseName,
            pulseFeatures = pulseFeatures,
            suitable = suitable,
            forbidden = forbidden,
            timeString = getCurrentTimeString(),
            pulseType = pulseType
        )

        _recordData.value = listOf(newRecord) + _recordData.value
        _currentPulseResult.value = newPulseData
    }

    /**
     * 模拟一键测量功能（保留用于测试）
     */
    fun performMeasurement() {
        // 模拟心率测量结果
        val simulatedHeartRate = (60..100).random()
        processMeasurementResult(simulatedHeartRate)
    }
    
    /**
     * 模拟iWatch测量功能
     */
    fun performWatchMeasurement() {
        // TODO: 实现iWatch测量逻辑
        // 暂时显示说明弹窗
    }
    
    /**
     * 保存完整的脉象测量记录
     * @param pulseResultModel 完整的测量结果数据
     */
    fun addPulseRecord(pulseResultModel: PulseResultModel) {
        android.util.Log.d("HomeDataManager", "addPulseRecord called with pulse type: ${pulseResultModel.pulseType}")

        val newRecord = PulseRecord(
            id = UUID.randomUUID().toString(),
            pulseName = pulseResultModel.pulseType,
            timeString = pulseResultModel.measurementTime,
            pulseType = getPulseTypeFromString(pulseResultModel.pulseType),
            pulseResultModel = pulseResultModel
        )

        android.util.Log.d("HomeDataManager", "Created new record with ID: ${newRecord.id}")

        // 添加到完整记录列表
        _pulseRecords.value = listOf(newRecord) + _pulseRecords.value
        android.util.Log.d("HomeDataManager", "Updated pulse records list, now has ${_pulseRecords.value.size} records")

        // 同时更新简单记录列表（用于主页显示）
        _recordData.value = listOf(newRecord.toRecordData()) + _recordData.value
        android.util.Log.d("HomeDataManager", "Updated record data list, now has ${_recordData.value.size} records")

        // 更新当前脉象结果
        val newPulseData = PulseData(
            pulseName = pulseResultModel.pulseType,
            pulseFeatures = pulseResultModel.pulseFeatureText,
            suitable = pulseResultModel.suggestions["suitable"]?.split(",") ?: emptyList(),
            forbidden = pulseResultModel.suggestions["forbidden"]?.split(",") ?: emptyList(),
            timeString = pulseResultModel.measurementTime,
            pulseType = getPulseTypeFromString(pulseResultModel.pulseType)
        )
        _currentPulseResult.value = newPulseData

        // 持久化保存数据
        android.util.Log.d("HomeDataManager", "About to call savePulseRecords()")
        savePulseRecords()
        android.util.Log.d("HomeDataManager", "savePulseRecords() call completed")
    }

    /**
     * 根据脉象名称获取脉象类型
     */
    private fun getPulseTypeFromString(pulseName: String): PulseType {
        return when {
            pulseName.contains("平脉") -> PulseType.PING_MAI
            pulseName.contains("洪数") -> PulseType.HONG_SHU_MAI
            pulseName.contains("滑数") -> PulseType.HUA_SHU_MAI
            pulseName.contains("细弱") -> PulseType.XI_RUO_MAI
            pulseName.contains("细数") -> PulseType.XI_SHU_MAI
            pulseName.contains("涩数") -> PulseType.SE_SHU_MAI
            pulseName.contains("弦滑") -> PulseType.XUAN_HUA_MAI
            pulseName.contains("弦结") -> PulseType.XUAN_JIE_MAI
            pulseName.contains("弦数") -> PulseType.XUAN_SHU_MAI
            pulseName.contains("弱迟") -> PulseType.RUO_CHI_MAI
            pulseName.contains("数脉") -> PulseType.SHU_MAI
            pulseName.contains("迟脉") -> PulseType.CHI_MAI
            else -> PulseType.PING_MAI
        }
    }

    /**
     * 根据记录ID获取完整的脉象记录
     */
    fun getPulseRecordById(id: String): PulseRecord? {
        return _pulseRecords.value.find { it.id == id }
    }

    /**
     * 获取最新记录的ID
     */
    fun getLatestRecordId(): String? {
        return _pulseRecords.value.firstOrNull()?.id
    }

    /**
     * 获取最新的脉象记录
     */
    fun getLatestPulseRecord(): PulseRecord? {
        return _pulseRecords.value.firstOrNull()
    }

    /**
     * 切换订阅状态（用于测试）
     */
    fun toggleSubscription() {
        _isSubscribed.value = !_isSubscribed.value
        saveSubscriptionStatus()
    }

    /**
     * 加载持久化的脉象记录数据
     */
    private fun loadPulseRecords() {
        try {
            val recordsJson = sharedPrefs.getString(KEY_PULSE_RECORDS, null)
            android.util.Log.d("HomeDataManager", "Loading pulse records, JSON exists: ${recordsJson != null}")
            if (recordsJson != null) {
                android.util.Log.d("HomeDataManager", "JSON length: ${recordsJson.length}")
                val type = object : TypeToken<List<PulseRecord>>() {}.type
                val records: List<PulseRecord> = gson.fromJson(recordsJson, type)
                _pulseRecords.value = records
                _recordData.value = records.map { it.toRecordData() }
                android.util.Log.d("HomeDataManager", "Successfully loaded ${records.size} pulse records")

                // 更新当前脉象结果为最新记录
                if (records.isNotEmpty()) {
                    val latestRecord = records.first()
                    val newPulseData = PulseData(
                        pulseName = latestRecord.pulseName,
                        pulseFeatures = latestRecord.pulseResultModel.pulseFeatureText,
                        suitable = latestRecord.pulseResultModel.suggestions["suitable"]?.split(",") ?: emptyList(),
                        forbidden = latestRecord.pulseResultModel.suggestions["forbidden"]?.split(",") ?: emptyList(),
                        timeString = latestRecord.timeString,
                        pulseType = latestRecord.pulseType
                    )
                    _currentPulseResult.value = newPulseData
                }
            } else {
                android.util.Log.d("HomeDataManager", "No saved pulse records found")
            }
        } catch (e: Exception) {
            // 如果加载失败，清除损坏的数据并使用空列表
            android.util.Log.e("HomeDataManager", "Failed to load pulse records", e)
            sharedPrefs.edit().remove(KEY_PULSE_RECORDS).apply()
            _pulseRecords.value = emptyList()
            _recordData.value = emptyList()
        }
    }

    /**
     * 保存脉象记录数据到SharedPreferences
     */
    private fun savePulseRecords() {
        try {
            val recordsJson = gson.toJson(_pulseRecords.value)
            sharedPrefs.edit()
                .putString(KEY_PULSE_RECORDS, recordsJson)
                .apply()
            android.util.Log.d("HomeDataManager", "Successfully saved ${_pulseRecords.value.size} pulse records")
        } catch (e: Exception) {
            // 保存失败时记录日志，但不影响应用运行
            android.util.Log.e("HomeDataManager", "Failed to save pulse records", e)
        }
    }

    /**
     * 加载订阅状态
     */
    private fun loadSubscriptionStatus(): Boolean {
        return sharedPrefs.getBoolean(KEY_SUBSCRIPTION_STATUS, false)
    }

    /**
     * 保存订阅状态
     */
    private fun saveSubscriptionStatus() {
        sharedPrefs.edit()
            .putBoolean(KEY_SUBSCRIPTION_STATUS, _isSubscribed.value)
            .apply()
    }
    
    /**
     * 删除指定的记录
     */
    fun deleteRecords(recordIds: List<String>) {
        try {
            // 从脉象记录中删除
            val updatedPulseRecords = _pulseRecords.value.filterNot { record ->
                recordIds.contains(record.id)
            }
            _pulseRecords.value = updatedPulseRecords

            // 从记录数据中删除
            val updatedRecordData = _recordData.value.filterNot { record ->
                recordIds.contains(record.id)
            }
            _recordData.value = updatedRecordData

            // 如果删除的是当前显示的脉象结果，清空当前结果
            if (_pulseRecords.value.isEmpty()) {
                _currentPulseResult.value = null
            } else {
                // 检查当前结果是否被删除，如果是则更新为最新的记录
                val currentResult = _currentPulseResult.value
                if (currentResult != null) {
                    val stillExists = _pulseRecords.value.any { record ->
                        record.timeString == currentResult.timeString &&
                        record.pulseName == currentResult.pulseName
                    }
                    if (!stillExists) {
                        // 当前结果被删除，更新为最新记录
                        val latestRecord = _pulseRecords.value.firstOrNull()
                        _currentPulseResult.value = latestRecord?.let { record ->
                            PulseData(
                                pulseName = record.pulseName,
                                pulseFeatures = record.pulseResultModel.pulseFeatureText,
                                suitable = record.pulseResultModel.suggestions["suitable"]?.split(",") ?: emptyList(),
                                forbidden = record.pulseResultModel.suggestions["forbidden"]?.split(",") ?: emptyList(),
                                timeString = record.timeString,
                                pulseType = getPulseTypeFromString(record.pulseName)
                            )
                        }
                    }
                }
            }

            // 持久化保存
            savePulseRecords()

            android.util.Log.d("HomeDataManager", "Successfully deleted ${recordIds.size} records")
        } catch (e: Exception) {
            android.util.Log.e("HomeDataManager", "Failed to delete records", e)
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        _currentTimeData.value = getCurrentTimeDataByHour()
        // TODO: 从服务器获取最新数据
    }
    
    private fun getCurrentTimeString(): String {
        val formatter = SimpleDateFormat("yyyy/MM/dd HH:mm", Locale.getDefault())
        return formatter.format(Date())
    }
    

    
    private fun getCurrentTimeDataByHour(): TimeData {
        val calendar = Calendar.getInstance()
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        
        return when (hour) {
            23, 0 -> TimeData("23:00-01:00", "胆经当令", "睡前泡脚，暖身助入眠", "子时")
            1, 2 -> TimeData("01:00-03:00", "肝经当令", "深度睡眠，养肝血", "丑时")
            3, 4 -> TimeData("03:00-05:00", "肺经当令", "熟睡中，肺部排毒", "寅时")
            5, 6 -> TimeData("05:00-07:00", "大肠经当令", "起床排便，清肠胃", "卯时")
            7, 8 -> TimeData("07:00-09:00", "胃经当令", "早餐时间，养胃气", "辰时")
            9, 10 -> TimeData("09:00-11:00", "脾经当令", "工作学习，健脾胃", "巳时")
            11, 12 -> TimeData("11:00-13:00", "心经当令", "午餐小憩，养心神", "午时")
            13, 14 -> TimeData("13:00-15:00", "小肠经当令", "午后工作，助消化", "未时")
            15, 16 -> TimeData("15:00-17:00", "膀胱经当令", "下午茶时，利水道", "申时")
            17, 18 -> TimeData("17:00-19:00", "肾经当令", "晚餐时间，补肾气", "酉时")
            19, 20 -> TimeData("19:00-21:00", "心包经当令", "散步休闲，护心脏", "戌时")
            else -> TimeData("21:00-23:00", "三焦经当令", "准备睡眠，调三焦", "亥时")
        }
    }
}

/**
 * 全局数据管理器实例 - 需要在Application中初始化
 */
lateinit var homeDataManager: HomeDataManager
    private set

/**
 * 初始化全局数据管理器
 */
fun initializeHomeDataManager(context: Context) {
    homeDataManager = HomeDataManager(context.applicationContext)
}
