package cn.flyok.bamai.data

import android.content.Context
import cn.flyok.bamai.network.NetworkAPI
import cn.flyok.bamai.network.NetworkResult
import kotlinx.coroutines.delay

/**
 * 登录管理器 - 复刻iOS端登录逻辑
 * 
 * 功能：
 * - 设备ID自动登录
 * - 手机号登录
 * - 手机号绑定
 * - 登录状态管理
 */
class LoginManager(private val context: Context) {

    private val userManager = UserManager.getInstance(context)
    
    /**
     * 设备ID自动登录 - 复刻iOS端performAutoLogin
     */
    suspend fun performDeviceLogin(): NetworkResult<LoginResponse> {
        return try {
            val deviceInfo = DeviceIdManager.getDeviceInfo(context)
            val params = deviceInfo.toNetworkParams()
            
            android.util.Log.d("LoginManager", "开始设备ID登录: ${deviceInfo.deviceId}")
            
            // TODO: 实际网络请求
            // val response = networkManager.post(NetworkAPI.API_LOGIN, params)
            
            // 模拟网络请求延迟
            delay(1000)
            
            // 模拟成功响应 - 实际应该从网络获取
            val mockResponse = LoginResponse(
                token = "mock_token_${System.currentTimeMillis()}",
                userId = "mock_user_${deviceInfo.deviceId.hashCode()}",
                userIdStr = "BM${System.currentTimeMillis()}",
                username = "用户${deviceInfo.deviceId.takeLast(4)}",
                type = 0, // 默认非会员
                status = 1,
                vipEndTime = "",
                vipDuration = 0
            )
            
            // 保存用户信息
            userManager.saveUserInfo(
                token = mockResponse.token,
                userId = mockResponse.userId,
                userIdStr = mockResponse.userIdStr,
                username = mockResponse.username,
                type = mockResponse.type,
                status = mockResponse.status,
                vipEndTime = mockResponse.vipEndTime,
                vipDuration = mockResponse.vipDuration
            )
            
            android.util.Log.d("LoginManager", "设备ID登录成功: ${mockResponse.userIdStr}")
            NetworkResult.Success(mockResponse)
            
        } catch (e: Exception) {
            android.util.Log.e("LoginManager", "设备ID登录失败", e)
            NetworkResult.Exception(e)
        }
    }
    

    

    

    

    
    /**
     * 刷新用户信息
     */
    suspend fun refreshUserInfo(): NetworkResult<LoginResponse> {
        return try {
            if (!userManager.isLoggedIn) {
                return NetworkResult.Error(401, "用户未登录")
            }
            
            android.util.Log.d("LoginManager", "刷新用户信息")
            
            // TODO: 实际网络请求
            // val response = networkManager.get(NetworkAPI.API_USER_INFO, token)
            
            // 模拟网络请求延迟
            delay(500)
            
            // 模拟返回最新用户信息
            val currentUser = userManager
            val mockResponse = LoginResponse(
                token = currentUser.token.value,
                userId = currentUser.userId.value,
                userIdStr = currentUser.userIdStr.value,
                username = currentUser.username.value,
                type = currentUser.type.value,
                status = currentUser.status.value,
                vipEndTime = currentUser.vipEndTime.value,
                vipDuration = currentUser.vipDuration.value
            )
            
            android.util.Log.d("LoginManager", "用户信息刷新成功")
            NetworkResult.Success(mockResponse)
            
        } catch (e: Exception) {
            android.util.Log.e("LoginManager", "刷新用户信息失败", e)
            NetworkResult.Exception(e)
        }
    }
    
    /**
     * 登出
     */
    fun logout() {
        android.util.Log.d("LoginManager", "用户登出")
        userManager.logout()
    }
}
