package cn.flyok.bamai.data

import android.annotation.SuppressLint
import android.content.Context
import android.provider.Settings
import java.util.*

/**
 * 设备ID管理器 - 复刻iOS端设备标识符逻辑
 * 
 * 功能：
 * - 生成和管理设备唯一标识符
 * - 支持设备ID持久化存储
 * - 提供设备信息获取
 */
object DeviceIdManager {
    
    private const val PREFS_NAME = "bamai_device_info"
    private const val KEY_DEVICE_ID = "device_id"
    private const val KEY_DEVICE_MODEL = "device_model"
    private const val KEY_DEVICE_BRAND = "device_brand"
    private const val KEY_SYSTEM_VERSION = "system_version"
    
    /**
     * 获取设备唯一标识符
     * 复刻iOS端的getDeviceIdentifier逻辑
     */
    fun getDeviceIdentifier(context: Context): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        // 先尝试从本地存储获取
        val savedDeviceId = prefs.getString(KEY_DEVICE_ID, null)
        if (!savedDeviceId.isNullOrEmpty()) {
            android.util.Log.d("DeviceIdManager", "Device ID from storage: $savedDeviceId")
            return savedDeviceId
        }
        
        // 生成新的设备ID
        val newDeviceId = generateDeviceId(context)
        
        // 保存到本地存储
        prefs.edit()
            .putString(KEY_DEVICE_ID, newDeviceId)
            .putString(KEY_DEVICE_MODEL, android.os.Build.MODEL)
            .putString(KEY_DEVICE_BRAND, android.os.Build.BRAND)
            .putString(KEY_SYSTEM_VERSION, android.os.Build.VERSION.RELEASE)
            .apply()
        
        android.util.Log.d("DeviceIdManager", "Generated new Device ID: $newDeviceId")
        return newDeviceId
    }
    
    /**
     * 生成设备ID
     * 优先级：Android ID > UUID
     */
    @SuppressLint("HardwareIds")
    private fun generateDeviceId(context: Context): String {
        return try {
            // 优先使用Android ID
            val androidId = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ANDROID_ID
            )
            
            if (!androidId.isNullOrEmpty() && androidId != "9774d56d682e549c") {
                // Android ID有效（排除已知的无效值）
                "android_$androidId"
            } else {
                // 生成UUID作为备选方案
                "uuid_${UUID.randomUUID()}"
            }
        } catch (e: Exception) {
            // 异常情况下生成UUID
            android.util.Log.e("DeviceIdManager", "Failed to generate device ID", e)
            "uuid_${UUID.randomUUID()}"
        }
    }
    
    /**
     * 获取设备信息
     */
    fun getDeviceInfo(context: Context): DeviceInfo {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        return DeviceInfo(
            deviceId = getDeviceIdentifier(context),
            model = prefs.getString(KEY_DEVICE_MODEL, android.os.Build.MODEL) ?: android.os.Build.MODEL,
            brand = prefs.getString(KEY_DEVICE_BRAND, android.os.Build.BRAND) ?: android.os.Build.BRAND,
            systemVersion = prefs.getString(KEY_SYSTEM_VERSION, android.os.Build.VERSION.RELEASE) ?: android.os.Build.VERSION.RELEASE,
            appVersion = getAppVersion(context)
        )
    }
    
    /**
     * 获取应用版本
     */
    private fun getAppVersion(context: Context): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0.0"
        } catch (e: Exception) {
            "1.0.0"
        }
    }
    
    /**
     * 清除设备ID（用于测试或重置）
     */
    fun clearDeviceId(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().clear().apply()
        android.util.Log.d("DeviceIdManager", "Device ID cleared")
    }
    
    /**
     * 检查设备ID是否存在
     */
    fun hasDeviceId(context: Context): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return !prefs.getString(KEY_DEVICE_ID, null).isNullOrEmpty()
    }
}

/**
 * 设备信息数据类
 */
data class DeviceInfo(
    val deviceId: String,
    val model: String,
    val brand: String,
    val systemVersion: String,
    val appVersion: String
) {
    /**
     * 转换为网络请求参数
     */
    fun toNetworkParams(): Map<String, Any> {
        return mapOf(
            "deviceIdentifier" to deviceId,
            "deviceModel" to model,
            "deviceBrand" to brand,
            "systemVersion" to systemVersion,
            "appVersion" to appVersion,
            "platform" to "android"
        )
    }
}
