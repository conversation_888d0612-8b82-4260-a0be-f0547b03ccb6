package cn.flyok.bamai.data

/**
 * 订阅套餐数据模型
 */
data class SubscriptionPlan(
    val id: String,
    val name: String,
    val description: String,
    val price: Double,
    val originalPrice: Double? = null, // 原价，用于显示折扣
    val duration: Int, // 天数
    val isPopular: Boolean = false, // 是否为推荐套餐
    val features: List<String> = emptyList(), // 功能特性
    val discount: String? = null // 折扣标签，如"限时优惠"
)

/**
 * 支付方式
 */
enum class PaymentMethod(val displayName: String, val code: String) {
    WECHAT("微信支付", "wechat"),
    ALIPAY("支付宝", "alipay"),
    APPLE_PAY("Apple Pay", "apple_pay")
}

/**
 * 订单状态
 */
enum class OrderStatus(val code: Int, val displayName: String) {
    PENDING(0, "待支付"),
    PAID(1, "已支付"),
    CANCELLED(2, "已取消"),
    EXPIRED(3, "已过期"),
    REFUNDED(4, "已退款")
}

/**
 * 订单信息
 */
data class OrderInfo(
    val orderId: String,
    val planId: String,
    val planName: String,
    val price: Double,
    val paymentMethod: PaymentMethod,
    val status: OrderStatus,
    val createTime: Long,
    val expireTime: Long,
    val payTime: Long? = null
)

/**
 * 微信支付预下单响应
 */
data class WechatPayResponse(
    val prepayId: String,
    val appId: String,
    val partnerId: String,
    val packageValue: String,
    val nonceStr: String,
    val timeStamp: String,
    val sign: String
)

/**
 * 支付结果
 */
sealed class PaymentResult {
    object Success : PaymentResult()
    object Cancelled : PaymentResult()
    data class Failed(val error: String) : PaymentResult()
}

/**
 * 订阅管理器响应
 */
sealed class SubscriptionResult<T> {
    data class Success<T>(val data: T) : SubscriptionResult<T>()
    data class Error<T>(val code: Int, val message: String) : SubscriptionResult<T>()
    data class Exception<T>(val exception: Throwable) : SubscriptionResult<T>()
}
