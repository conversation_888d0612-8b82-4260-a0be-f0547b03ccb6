package cn.flyok.bamai.data

import android.content.Context
import android.content.SharedPreferences
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.State

/**
 * 用户管理器 - 完全复刻iOS端FlyUserInfo
 *
 * 功能：
 * - 设备ID匿名登录
 * - 用户信息管理
 * - 会员状态管理
 */
class UserManager private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: UserManager? = null
        
        fun getInstance(context: Context): UserManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UserManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // SharedPreferences键值
        private const val PREFS_NAME = "bamai_user_info"
        private const val KEY_TOKEN = "token"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USER_ID_STR = "user_id_str"
        private const val KEY_USERNAME = "username"
        private const val KEY_TYPE = "type" // 0: 普通用户, 1: 会员
        private const val KEY_STATUS = "status"
        private const val KEY_VIP_END_TIME = "vip_end_time"
        private const val KEY_VIP_DURATION = "vip_duration" // -1: 永久会员
    }
    
    private val sharedPrefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // 用户信息状态
    private val _token = mutableStateOf(loadString(KEY_TOKEN))
    val token: State<String> = _token
    
    private val _userId = mutableStateOf(loadString(KEY_USER_ID))
    val userId: State<String> = _userId
    
    private val _userIdStr = mutableStateOf(loadString(KEY_USER_ID_STR))
    val userIdStr: State<String> = _userIdStr
    
    private val _username = mutableStateOf(loadString(KEY_USERNAME))
    val username: State<String> = _username

    private val _type = mutableStateOf(loadInt(KEY_TYPE))
    val type: State<Int> = _type
    
    private val _status = mutableStateOf(loadInt(KEY_STATUS))
    val status: State<Int> = _status
    
    private val _vipEndTime = mutableStateOf(loadString(KEY_VIP_END_TIME))
    val vipEndTime: State<String> = _vipEndTime
    
    private val _vipDuration = mutableStateOf(loadInt(KEY_VIP_DURATION))
    val vipDuration: State<Int> = _vipDuration
    
    /**
     * 是否已登录
     */
    val isLoggedIn: Boolean
        get() = _token.value.isNotEmpty()
    
    /**
     * 是否为会员
     */
    val isVip: Boolean
        get() = _type.value == 1
    
    /**
     * 保存用户信息（设备ID登录后）
     */
    fun saveUserInfo(
        token: String,
        userId: String,
        userIdStr: String,
        username: String = "",
        type: Int = 0,
        status: Int = 0,
        vipEndTime: String = "",
        vipDuration: Int = 0
    ) {
        _token.value = token
        _userId.value = userId
        _userIdStr.value = userIdStr
        _username.value = username
        _type.value = type
        _status.value = status
        _vipEndTime.value = vipEndTime
        _vipDuration.value = vipDuration
        
        // 保存到SharedPreferences
        with(sharedPrefs.edit()) {
            putString(KEY_TOKEN, token)
            putString(KEY_USER_ID, userId)
            putString(KEY_USER_ID_STR, userIdStr)
            putString(KEY_USERNAME, username)
            putInt(KEY_TYPE, type)
            putInt(KEY_STATUS, status)
            putString(KEY_VIP_END_TIME, vipEndTime)
            putInt(KEY_VIP_DURATION, vipDuration)
            apply()
        }
    }
    

    
    /**
     * 更新会员状态
     */
    fun updateVipStatus(type: Int, vipEndTime: String, vipDuration: Int) {
        _type.value = type
        _vipEndTime.value = vipEndTime
        _vipDuration.value = vipDuration
        
        with(sharedPrefs.edit()) {
            putInt(KEY_TYPE, type)
            putString(KEY_VIP_END_TIME, vipEndTime)
            putInt(KEY_VIP_DURATION, vipDuration)
            apply()
        }
    }
    
    /**
     * 清除用户信息（登出）
     */
    fun logout() {
        _token.value = ""
        _userId.value = ""
        _userIdStr.value = ""
        _username.value = ""
        _type.value = 0
        _status.value = 0
        _vipEndTime.value = ""
        _vipDuration.value = 0
        
        sharedPrefs.edit().clear().apply()
    }
    
    // 私有辅助方法
    private fun loadString(key: String): String = sharedPrefs.getString(key, "") ?: ""
    private fun loadInt(key: String): Int = sharedPrefs.getInt(key, 0)
}

/**
 * 登录响应数据类 - 对应iOS端LoginResponse
 */
data class LoginResponse(
    val token: String,
    val userId: String,
    val userIdStr: String,
    val username: String,
    val type: Int, // 对应iOS端的isvip字段
    val status: Int,
    val vipEndTime: String,
    val vipDuration: Int
)

/**
 * 全局用户管理器实例
 */
lateinit var userManager: UserManager
    private set

/**
 * 初始化用户管理器
 */
fun initializeUserManager(context: Context) {
    userManager = UserManager.getInstance(context)
}
