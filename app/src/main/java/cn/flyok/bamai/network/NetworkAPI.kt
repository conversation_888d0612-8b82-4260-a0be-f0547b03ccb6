package cn.flyok.bamai.network

/**
 * 网络API接口定义 - 复刻iOS端NetWorkAPI
 * 
 * 包含所有后端接口的路径定义
 */
object NetworkAPI {
    
    // 基础配置
    const val BASE_URL = "https://api.bamai.com/" // TODO: 替换为实际的服务器地址
    
    // ==================== 用户认证相关 ====================
    
    /**
     * 设备ID登录接口 - 复刻iOS端api_login
     * POST /api/v1/consumer/login
     * 参数：deviceIdentifier, deviceModel, deviceBrand, systemVersion, appVersion, platform
     * 返回：token, userId, userIdStr, username, type(isvip), status, vipEndTime, vipDuration
     */
    const val API_LOGIN = "api/v1/consumer/login"
    
    /**
     * 获取用户信息接口 - 复刻iOS端api_userInfo
     * GET /api/v1/consumer/android/login-user-info
     * 需要token认证
     * 返回：用户完整信息包括会员状态
     */
    const val API_USER_INFO = "api/v1/consumer/android/login-user-info"
    

    
    // ==================== 支付相关 ====================
    
    /**
     * Android支付验证接口 - 对应iOS端api_applePay
     * POST /payment/androidPay
     * 参数：userId, productId, purchaseToken, orderId, packageName
     * 需要token认证
     * 返回：success, message, vipEndTime, vipDuration
     */
    const val API_ANDROID_PAY = "payment/androidPay"
    
    /**
     * 恢复购买接口
     * POST /payment/restore-purchase
     * 参数：userId, platform(android)
     * 需要token认证
     * 返回：success, message, vipEndTime, vipDuration
     */
    const val API_RESTORE_PURCHASE = "payment/restore-purchase"
    
    // ==================== 其他功能 ====================
    
    /**
     * 问题反馈接口 - 复刻iOS端api_feedBack
     * POST /api/v1/feedback/add
     * 参数：content, contact, type
     * 需要token认证
     * 返回：success, message
     */
    const val API_FEEDBACK = "api/v1/feedback/add"
    
    /**
     * 获取隐私政策内容
     * GET /api/v1/content/privacy-policy
     * 返回：title, content, updateTime
     */
    const val API_PRIVACY_POLICY = "api/v1/content/privacy-policy"
    
    /**
     * 获取用户协议内容
     * GET /api/v1/content/user-agreement
     * 返回：title, content, updateTime
     */
    const val API_USER_AGREEMENT = "api/v1/content/user-agreement"
    
    // ==================== 请求头定义 ====================
    
    /**
     * 认证头名称
     */
    const val HEADER_AUTHORIZATION = "Authorization"
    
    /**
     * 内容类型头
     */
    const val HEADER_CONTENT_TYPE = "Content-Type"
    
    /**
     * 应用版本头
     */
    const val HEADER_APP_VERSION = "App-Version"
    
    /**
     * 平台标识头
     */
    const val HEADER_PLATFORM = "Platform"
    
    // ==================== 响应状态码 ====================
    
    /**
     * 成功状态码
     */
    const val STATUS_SUCCESS = 200
    
    /**
     * 未授权状态码
     */
    const val STATUS_UNAUTHORIZED = 401
    
    /**
     * 参数错误状态码
     */
    const val STATUS_BAD_REQUEST = 400
    
    /**
     * 服务器错误状态码
     */
    const val STATUS_SERVER_ERROR = 500
}

/**
 * 网络请求结果封装
 */
sealed class NetworkResult<T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error<T>(val code: Int, val message: String) : NetworkResult<T>()
    data class Exception<T>(val throwable: Throwable) : NetworkResult<T>()
}

/**
 * 通用API响应格式
 */
data class ApiResponse<T>(
    val code: Int,
    val message: String,
    val data: T?
)


